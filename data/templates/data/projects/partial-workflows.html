{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}
{% get_current_language as LANGUAGE_CODE %}

<div class="{% include "data/utility/tab-pane.html" %}" role="tabpanel" style="z-index: 4;">
    <div class="{% include "data/utility/table-nav.html" %}">
        <div class="{% include "data/utility/table-content.html" %}" id="workflow-view-container">
            <div class="max-md:tw-hidden w-75 d-flex align-items-center">
                <div class="nav-item fs-6 text-gray-900">
                    <div class="d-flex align-items-center justify-content-center">
                        <button type="button" class="{% include "data/utility/view-plus-link.html" %} view-wizard-button" 
                            hx-get="{% url 'commerce_view_setting' %}" 
                            hx-vals='{"object_type": "workflow", "view_button":"create"}' 
                            hx-target="#view-drawer" 
                            hx-swap="innerHTML"
                            >
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-plus" viewBox="0 0 16 16">
                                <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                            </svg>
                        </button>
                    </div>
                </div>

                {% include "data/projects/partial-dropdown-view-menu.html" with hide_archived=True %}

                <div class="{% include "data/utility/view-menu-nav-item.html" %}">
                    <a class="{% include "data/utility/view-menu-default.html" %}"
                        type="button"
                        href = "{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}">
                        <span class="svg-icon svg-icon-muted svg-icon-2"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M22 12C22 17.5 17.5 22 12 22C6.5 22 2 17.5 2 12C2 6.5 6.5 2 12 2C17.5 2 22 6.5 22 12ZM12 6C8.7 6 6 8.7 6 12C6 15.3 8.7 18 12 18C15.3 18 18 15.3 18 12C18 8.7 15.3 6 12 6Z" fill="currentColor"/>
                            </svg>
                        </span>
                    </a>
                    {% if not view_filter.view.title %}
                        <div class="text-gray-900 w-20px nav-item justify-content-center d-flex fs-6">
                            <button type="button" class="{% include "data/utility/view-plus-link.html" %} view-wizard-button"
                                hx-vals='{"module": "{{menu_key}}", "object_type": "workflow", "view_id":"{{view.id}}"}'
                                hx-get="{% url 'commerce_view_setting' %}" 
                                hx-target="#view-drawer"
                                
                                >
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-three-dots-vertical" viewBox="0 0 16 16">
                                    <path d="M9.5 13a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z"/>
                                </svg>

                            </button>
                        </div>
                    {% endif %}
                </div>

                {% include 'data/projects/partial-view-menu.html' %}

            </div>

            {% comment %} Mobile View {% endcomment %}
            <div class="tw-hidden max-md:tw-flex tw-w-1/2">
                <div class="d-flex align-items-center">
                    <!-- Example split danger button -->
                    <div class="mb-2 btn-group">
                        <button type="button" class="tw-max-w-[90px] align-items-center d-flex btn bg-white border btn-md tw-px-[10px] view-wizard-button"
                            style="height: 26px; border-radius: 0.475rem 0 0 0.475rem;"
                            hx-vals='{"object_type": "workflow", "view_id":"{{view.id}}"}'
                            hx-get="{% url 'commerce_view_setting' %}" 
                            hx-target="#view-drawer"
                        >
                            <span class="fs-7 ps-1 fw-bolder tw-text-ellipsis tw-overflow-hidden tw-text-nowrap">
                                {% if view_filter.view.title %}
                                    {{ view_filter.view.title }}
                                {% else %}
                                    {% if LANGUAGE_CODE == 'ja'%}ビュー{% else %}View{% endif %}
                                {% endif %}
                            </span>
                        </button>
                        <button type="button"
                            class="tw-w-[30px] align-items-center d-flex btn btn-white border btn-md  dropdown-toggle py-1 tw-pl-2 dropdown-toggle-split"
                            data-bs-toggle="dropdown"
                            aria-expanded="false"
                            style="height: 26px; border-radius: 0 0.475rem 0.475rem 0;"
                        >
                            <span class="svg-icon svg-icon-4">
                                <svg width=24 height=24 xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" fill="currentColor">
                                    <path d="M137.4 374.6c12.5 12.5 32.8 12.5 45.3 0l128-128c9.2-9.2 11.9-22.9 6.9-34.9s-16.6-19.8-29.6-19.8L32 192c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9l128 128z"/>
                                </svg>
                            </span>
                        </button>
                        <ul class="dropdown-menu tw-max-w-[180px] tw-max-h-[300px] tw-overflow-y-scroll">
                            <li><a class="dropdown-item tw-text-ellipsis tw-overflow-hidden" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}">{% if LANGUAGE_CODE == 'ja' %}デフォルト{% else %}Default View{% endif %}</a></li>
                            <div class="dropdown-divider"></div>
                            {% for view in views %}
                                {% if view.title %}
                                    <li><a class="dropdown-item tw-text-ellipsis tw-overflow-hidden" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?view_id={{view.id}}">{{view.title}}</a></li>
                                {% endif %}
                            {% endfor %}
                        </ul>
                    </div>
                    <div class="mb-2 text-gray-900 fs-6">
                        <button type="button" class="tw-border-0 tw-bg-transparent tw-text-gray-900 view-wizard-button"
                            hx-get="{% url 'commerce_view_setting' %}" 
                            hx-vals='{"object_type": "workflow", "view_button":"create"}' 
                            hx-target="#view-drawer" 
                            hx-trigger="click"
                            hx-swap="innerHTML">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-plus" viewBox="0 0 16 16">
                                <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <div class="d-flex w-50 justify-content-end">
                <div class="max-md:tw-hidden tw-flex me-2">
                    <div class="{% include "data/utility/search-wrapper.html" %}">
                        <div class="d-flex align-items-center">
                            <form id="filter-form-search" method="get" class="w-100">
                                <div class="mb-0 d-flex position-relative" style="height: 26px;">
                                    <span class="svg-icon svg-icon-3 search-icon-view">
                                        <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M13 13L10.1 10.1M11.6667 6.33333C11.6667 9.27885 9.27885 11.6667 6.33333 11.6667C3.38781 11.6667 1 9.27885 1 6.33333C1 3.38781 3.38781 1 6.33333 1C9.27885 1 11.6667 3.38781 11.6667 6.33333Z" stroke="#8E8C95" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                    </span>
                                    <input
                                    id="base-search-input" type="text" name="q" class="bg-white form-control ps-12 pe-16 tw-rounded-lg" 
                                    value="{% if advance_search_filter %}{% for k, v in advance_search_filter.items %}{% if k|search_custom_field_object_inventory:request %}{% with channel_column=k|search_custom_field_object_inventory:request %}{{channel_column.name}}{% endwith %}{% else %}{% with column_display=k|display_column_inventory:request %}{{column_display}}{% endwith %}{% endif %} {% translate_lang v.key|display_filter_rule:LANGUAGE_CODE LANGUAGE_CODE %} {{ v.value }}{% if not forloop.last %}, {% endif %}{% endfor %}{% elif search_q %}{{ search_q }}{% endif %}" 
                                    placeholder={% if LANGUAGE_CODE == 'ja' %} "検索" {% else %} "Search" {% endif %}
                                    onkeypress="if (event.keyCode == 13)document.forms['filter-form-search'].submit();"
                                    >
                                    <span class="svg-icon svg-icon-muted svg-icon-3 filter-icon filter-drawer-btn me-7"
                                        hx-get="{% url 'advance_search_drawer' %}"
                                        hx-vals='{"object_type": "{{object_type}}", "module": "{{menu_key}}", "search_scope":"True"}'
                                        hx-indicator=".loading-drawer-spinner"
                                        hx-target="#filter-drawer-content"
                                        hx-trigger="click"
                                        hx-swap="innerHTML"
                                    >
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                            <path fill="black" d="M19.43 12.98c.04-.32.07-.65.07-.98s-.03-.66-.07-.98l2.11-1.65a.5.5 0 0 0 .12-.63l-2-3.46a.5.5 0 0 0-.6-.22l-2.49 1a7.03 7.03 0 0 0-1.7-.98l-.38-2.65A.5.5 0 0 0 14 2h-4a.5.5 0 0 0-.5.45l-.38 2.65c-.63.25-1.21.57-1.74.98l-2.49-1a.5.5 0 0 0-.6.22l-2 3.46a.5.5 0 0 0 .12.63L4.57 11c-.04.32-.07.65-.07.98s.03.66.07.98l-2.11 1.65a.5.5 0 0 0-.12.63l2 3.46a.5.5 0 0 0 .6.22l2.49-1c.53.41 1.11.73 1.74.98l.38 2.65c.03.24.24.45.5.45h4c.26 0 .47-.21.5-.45l.38-2.65c.63-.25 1.21-.57 1.74-.98l2.49 1a.5.5 0 0 0 .6-.22l2-3.46a.5.5 0 0 0-.12-.63l-2.11-1.65zM12 15.5A3.5 3.5 0 1 1 12 8a3.5 3.5 0 0 1 0 7.5z"/>
                                        </svg>
                                    </span>
                                    <span class="svg-icon svg-icon-muted svg-icon-3 filter-icon filter-drawer-btn me-7"
                                        hx-get="{% url 'advance_search_drawer' %}"
                                        hx-vals='{"object_type": "{{object_type}}", "module": "{{menu_key}}", "search_scope":"True"}'
                                        hx-indicator=".loading-drawer-spinner"
                                        hx-target="#filter-drawer-content"
                                        hx-trigger="click"
                                        hx-swap="innerHTML"
                                    >
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                            <path fill="black" d="M19.43 12.98c.04-.32.07-.65.07-.98s-.03-.66-.07-.98l2.11-1.65a.5.5 0 0 0 .12-.63l-2-3.46a.5.5 0 0 0-.6-.22l-2.49 1a7.03 7.03 0 0 0-1.7-.98l-.38-2.65A.5.5 0 0 0 14 2h-4a.5.5 0 0 0-.5.45l-.38 2.65c-.63.25-1.21.57-1.74.98l-2.49-1a.5.5 0 0 0-.6.22l-2 3.46a.5.5 0 0 0 .12.63L4.57 11c-.04.32-.07.65-.07.98s.03.66.07.98l-2.11 1.65a.5.5 0 0 0-.12.63l2 3.46a.5.5 0 0 0 .6.22l2.49-1c.53.41 1.11.73 1.74.98l.38 2.65c.03.24.24.45.5.45h4c.26 0 .47-.21.5-.45l.38-2.65c.63-.25 1.21-.57 1.74-.98l2.49 1a.5.5 0 0 0 .6-.22l2-3.46a.5.5 0 0 0-.12-.63l-2.11-1.65zM12 15.5A3.5 3.5 0 1 1 12 8a3.5 3.5 0 0 1 0 7.5z"/>
                                        </svg>
                                    </span>
                                    <span class="svg-icon {% if advance_search.is_active %}svg-icon-primary{%else%}svg-icon-muted{% endif %} svg-icon-3 filter-icon filter-drawer-btn"
                                        hx-get="{% url 'advance_search_drawer' %}"
                                        hx-vals='{"object_type": "{{object_type}}", "module": "{{menu_key}}"}'
                                        hx-indicator=".loading-drawer-spinner"
                                        hx-target="#filter-drawer-content"
                                        hx-trigger="click"
                                        hx-swap="innerHTML"
                                    >
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M21 7H3C2.4 7 2 6.6 2 6V4C2 3.4 2.4 3 3 3H21C21.6 3 22 3.4 22 4V6C22 6.6 21.6 7 21 7Z" fill="currentColor"/>
                                            <path opacity="0.3" d="M21 14H3C2.4 14 2 13.6 2 13V11C2 10.4 2.4 10 3 10H21C21.6 10 22 10.4 22 11V13C22 13.6 21.6 14 21 14ZM22 20V18C22 17.4 21.6 17 21 17H3C2.4 17 2 17.4 2 18V20C2 20.6 2.4 21 3 21H21C21.6 21 22 20.6 22 20Z" fill="currentColor"/>
                                        </svg>
                                    </span>
                                    <input type="hidden" value="{{view_id}}" name="view_id">
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="{% include "data/utility/view-menu-search.html" %}">
                    <button
                        onclick="openSearch()"
                        class="tw-w-[28px] tw-h-[26px] tw-justify-center tw-items-center tw-border-0 tw-bg-transparent tw-mb-2 tw-cursor-pointer hover-tooltip" type="button"
                        >
                        <span class="search-wrapper-tooltip hover-tooltip-text">
                            {% if LANGUAGE_CODE == 'ja' %}検索{% else %}Search{% endif %}
                        </span>
                        <span class="tw-flex svg-icon svg-icon-3">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <path d="M21.7 18.9L18.6 15.8C17.9 16.9 16.9 17.9 15.8 18.6L18.9 21.7C19.3 22.1 19.9 22.1 20.3 21.7L21.7 20.3C22.1 19.9 22.1 19.3 21.7 18.9Z" fill="black" />
                                <path opacity="0.3" d="M11 20C6 20 2 16 2 11C2 6 6 2 11 2C16 2 20 6 20 11C20 16 16 20 11 20ZM11 4C7.1 4 4 7.1 4 11C4 14.9 7.1 18 11 18C14.9 18 18 14.9 18 11C18 7.1 14.9 4 11 4ZM8 11C8 9.3 9.3 8 11 8C11.6 8 12 7.6 12 7C12 6.4 11.6 6 11 6C8.2 6 6 8.2 6 11C6 11.6 6.4 12 7 12C7.6 12 8 11.6 8 11Z" fill="black" />
                            </svg>
                        </span>
                    </button>
                </div>

                {% if permission|check_permission:'edit' %}

                <button class="w-150px module-templates-btn align-items-center d-flex btn btn-bg-gray-100 border btn-md py-1 rounded-1 me-3" type="button"
                    style="height: 26px"
                    hx-get="{% host_url 'workflow_templates' host 'app' %}"
                    hx-target="#module-templates-content"
                    >
                    <span class="svg-icon svg-icon-3">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-files-alt" viewBox="0 0 16 16">
                            <path d="M11 0H3a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2 2 2 0 0 0 2-2V4a2 2 0 0 0-2-2 2 2 0 0 0-2-2m2 3a1 1 0 0 1 1 1v8a1 1 0 0 1-1 1zM2 2a1 1 0 0 1 1-1h8a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1z"/>
                        </svg>
                    </span>

                    <span class="fs-7 ps-1 fw-bolder w-125px">
                        {% if LANGUAGE_CODE == 'ja'%}
                        テンプレート
                        {% else %}
                        Templates
                        {% endif %}
                    </span>
                </button>
                {% endif %}
            </div>

        </div>

        <div id="workflow-bulk-action-container" class="d-none">
            <span class="me-10">
                <button class="py-1 rounded-1 btn btn-sm btn-light fw-bold mt-2 mb-1" onclick="selectAllWorkflows()">
                    <span>
                        {% if LANGUAGE_CODE == 'ja'%}
                        すべて選択
                        {% else %}
                        Select All
                        {% endif %}
                    </span>
                </button>
                <button class="py-1 rounded-1 btn btn-sm btn-light fw-bold mt-2 mb-1" onclick="deselectAllWorkflows()">
                    <span>
                        {% if LANGUAGE_CODE == 'ja'%}
                        選択を解除
                        {% else %}
                        Deselect All
                        {% endif %}
                    </span>
                </button>

                <script>
                    function selectAllWorkflows() {
                        selectWorkflowInputs = document.getElementsByClassName('workflow-selection')
                        for (var i = 0; i < selectWorkflowInputs.length; i++) {
                            selectWorkflowInputs[i].checked = true;
                        }
                        document.getElementById('flag_all').checked = true;
                    }
                    function deselectAllWorkflows() {
                        selectWorkflowInputs = document.getElementsByClassName('workflow-selection')
                        for (var i = 0; i < selectWorkflowInputs.length; i++) {
                            selectWorkflowInputs[i].checked = false;
                        }
                        document.getElementById('workflow-bulk-action-container').classList.add('d-none')
                        document.getElementById('workflow-view-container').classList.remove('d-none')
                        document.getElementById('flag_all').checked = false;    
                    }
                </script>
            </span>

            {% if permission|check_permission:'edit' %}
            <button class="py-1 rounded-1 btn btn-sm btn-light-primary fw-bold mt-2 mb-1" onclick="check_permission_action(event, 'edit', 'edit_workflow_bulk_modal')">
                <span>
                    {% if LANGUAGE_CODE == 'ja'%}
                    編集
                    {% else %}
                    Edit
                    {% endif %}
                </span>
            </button>
            <form class="d-none" 
            action="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}"
            method="GET">
                <button id="bulk_duplicate_workflow_btn" type="submit"></button>
            </form>
            <button
                type="button"
                class="py-1 rounded-1 btn btn-sm btn-light-warning fw-bold mt-2 mb-1"
                onclick="check_permission_action(event, 'edit')"
                name="bulk_duplicate"
                hx-target="#bulk_duplicate_workflow_btn"
                hx-post="{% host_url 'bulk_duplicate_workflow' host 'app' %}"
                hx-include="[name='selected_workflow']"
                hx-vals='{
                    "from": "{{ from }}", 
                    "target": "{{ target }}", 
                    "view_id": "{{ view_id }}"
                }'
                hx-on::after-request="document.getElementById('bulk_duplicate_workflow_btn').click()"
                >
                {% if LANGUAGE_CODE == 'ja' %}
                    複製
                {% else %}
                    Duplicate
                {% endif %}
            </button>
            {% endif %}

            {% if permission|check_permission:'archive' %}
            <button class="btn btn-sm btn-light-danger py-1 rounded-1 fw-bold mt-2 mb-1"
            hx-post="{% host_url 'bulk_delete_workflow' host 'app' %}" 
            hx-target="#workflow_list"
            hx-swap="outerHTML"
            hx-include="[name='selected_workflow']" hx-vals='{ {% if view_id %}"view_id":"{{view_id}}",{% endif %} "from":"{{from}}"}'
            onclick="check_permission_action(event, 'archive')" >
                {% if LANGUAGE_CODE == 'ja'%}
                削除する
                {% else %}
                Delete 
                {% endif %}
            </button>
            {% endif %}

            <script>
                document.body.addEventListener("hideWorkflowBulkActionBtn", function(evt){
                    document.getElementById('workflow-bulk-action-container').classList.add('d-none')
                    document.getElementById('workflow-view-container').classList.remove('d-none')
                })
            </script>
            
        </div>
    </div>
</div>

{% include 'data/projects/workflows-bulk-edit-modal.html' %}
<div id="project_order_response" class=""></div>
<div id="workflow-scrollable" class="scroll-x scroll-y w-100 pe-2" style="height: calc(100% - 60px)">
    <div class="pt-0 table-responsive">
        <table class="{% include "data/utility/table.html" %} workflows-table">
            <thead class="{% include "data/utility/table-header.html" %}" style="border-bottom: 1px solid #a4a4a4;">
                <tr class="align-middle">
                    <th></th>
                    {% for column in columns %}
                        {% with args=column|add:'|'|add:'workflow' %} 
                            {% with column_display=args|get_column_display:request %}
                                <th class="px-2 py-3 {% if forloop.counter == 1 %}mw-200px{% endif %}">
                                    {{column_display.name}}
                                </th>
                            {% endwith %}
                        {% endwith %}
                    {% endfor %}
                </tr>
            </thead>
            <tbody class="fs-6 w-100 p-0 {% if view_id %}sortable{% endif %} m-0" hx-post="{% host_url 'project_order' host 'app' %}" hx-vals='{"order_was_changed": "true"}' hx-include="[name=workflow], [name=view_id]" hx-trigger="end" hx-target="#project_order_response" id="workflow_list">
                {% if workflows %}

                    {% include 'data/projects/partial-workflows-rows.html' %}

                {% else %}
                <tr>
                    <td colspan="2">
                        {% if LANGUAGE_CODE == 'ja'%}
                        該当するワークフローがありません。
                        {% else %}
                        No Workflow Found.
                        {% endif %}
                    </td>
                </tr>
                {% endif %}
            </tbody>
        </table>
    </div>

    <input name='flag_all' id='flag_all' class="flag_all" hidden type="checkbox" ></input>
    <style>
        .workflow-item:hover .workflow-grip svg, .workflow-item:hover .workflow-new{
            color: #211F1C !important;
        }
        .workflow-item:hover .workflow-selection{
            border-width: 1px !important;
        }
    
        .new-workflow-indicator{
            display:none;
        }
    </style>
    
</div>

<div class="modal fade" id="moduleInitModal" tabindex="-1" aria-labelledby="moduleInitModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="moduleInitModalLabel">
                    {% if LANGUAGE_CODE == 'ja'%}
                    モジュール初期化の確認
                    {% else %}
                    Module Initialization Confirmation
                    {% endif %}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                {% if LANGUAGE_CODE == 'ja'%}
                ワークフローを初期化してもよろしいですか？ 初期化すると、既存のワークフローがすべて削除され、デフォルト設定にリセットされます。
                {% else %}
                Confirm if you're okay to initialize the workflows. This will remove all existing workflows and reset them to default settings.
                {% endif %}
            </div>
            <div class="modal-footer">
                <button type="button" class="btn bg-gray-200 border" data-bs-dismiss="modal">
                    {% if LANGUAGE_CODE == 'ja'%}
                    キャンセル
                    {% else %}
                    Cancel
                    {% endif %}
                </button>
                <form method="POST" action="{% host_url 'initialize_workflows' host 'app' %}">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-dark">
                        {% if LANGUAGE_CODE == 'ja'%}
                        確認
                        {% else %}
                        Confirm
                        {% endif %}
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>