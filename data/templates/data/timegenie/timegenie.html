{% extends 'base.html' %}
{% load tz %}
{% load custom_tags %}
{% load i18n %}
{% load hosts %}
{% load humanize %}
{% get_current_timezone as tz %} {# Get the current timezone #}
{% get_current_language as LANGUAGE_CODE %}


{% block content %}
<div>
    {% include 'data/static/tootip-search-wrapper.html' %}

    <div class="d-flex d-flex align-items-center" style="background-color: #FAFAFA !important;">
        
        {% include 'data/common/module-header-tabs.html' %}
        <div class="w-50 d-flex justify-content-end tw-mr-5" id="view-container">
            <div class="{% include "data/utility/header-action-button.html" %}">
                <button type="button" class="btn tw-font-[500] tw-rounded-l-lg tw-rounded-r-none tw-border-0 tw-bg-gray-200 hover:tw-bg-gray-300 max-md:tw-hidden w-125px align-items-center d-flex svg-icon-gray-600 py-1 justify-content-center px-1 manage-view-settings-button"
                    hx-get="{% url 'commerce_view_setting' %}" 
                    hx-target="#manage-contacts-view-settings-drawer"
                    hx-indicator=".loading-drawer-spinner,.view-form"
                    onclick="fillItemExportIds(this)"
                    style="width: 150px">
                    <span class="svg-icon svg-icon-4">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-upload" viewBox="0 0 16 16">
                            <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5"/>
                            <path d="M7.646 1.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 2.707V11.5a.5.5 0 0 1-1 0V2.707L5.354 4.854a.5.5 0 1 1-.708-.708z"/>
                        </svg>
                    </span>
                    <span class="">
                        {% if LANGUAGE_CODE == 'ja'%}
                        エクスポート
                        {% else %}
                        Export
                        {% endif %}
                    </span>
                </button>

                <button type="button" class="btn tw-font-[500] tw-rounded-l-none tw-rounded-r-lg tw-border-0 tw-bg-gray-200 hover:tw-bg-gray-300 max-md:tw-hidden w-125px align-items-center d-flex svg-icon-gray-600 py-1 justify-content-center px-1 manage-view-settings-button"
                    hx-get="{% url 'commerce_view_setting' %}" 
                    hx-target="#manage-contacts-view-settings-drawer"
                    onclick="fillItemIds(this)"
                    
                    >
                    <span class="svg-icon svg-icon-4">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-download" viewBox="0 0 16 16">
                            <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5"/>
                            <path d="M7.646 11.854a.5.5 0 0 0 .708 0l3-3a.5.5 0 0 0-.708-.708L8.5 10.293V1.5a.5.5 0 0 0-1 0v8.793L5.354 8.146a.5.5 0 1 0-.708.708z"/>
                        </svg>
                    </span>
                    <span class="">
                        {% if LANGUAGE_CODE == 'ja'%}
                        インポート
                        {% else %}
                        Import
                        {% endif %}
                    </span>
                </button>

                <script>
                    function fillItemIds(elm) {
                        // Call your JavaScript function to generate the account IDs dynamically
                        var checkboxes = document.querySelectorAll('input[type="checkbox"]');
                        var checkedIds = [];
                        checkboxes.forEach(function(checkbox) {
                            if (checkbox.checked) {
                                checkedIds.push(checkbox.value);
                            }
                        });
                        var itemIds = checkedIds;
                        itemIds = itemIds.filter(id => id !== 'on');
                        // Now set the hx-vals attribute with the updated account IDs
                        elm.setAttribute('hx-vals', '{"object_type": "{{object_type}}", "view_id":"{{view_filter.view.id}}", "download_view":true, "import_export_type":"import", "track_ids":"' + itemIds + '"}');

                    }
                </script>
            </div>
            <script>
                function fillItemExportIds(elm) {
                    // Call your JavaScript function to generate the account IDs dynamically
                    var checkboxes = document.querySelectorAll('input[type="checkbox"]');
                    var checkedIds = [];
                    checkboxes.forEach(function(checkbox) {
                        if (checkbox.checked) {
                            checkedIds.push(checkbox.value);
                        }
                    });
                    var itemIds = checkedIds;
                    itemIds = itemIds.filter(id => id !== 'on');
                    // Now set the hx-vals attribute with the updated account IDs

                    elm.setAttribute('hx-vals', '{"object_type": "{{object_type}}", "view_id":"{{view_filter.view.id}}", "download_view":"true", "import_export_type":"export", "track_ids":"' + itemIds + '"}');
                }
            </script>
            <div class="btn-group tw-h-[34px]">                            
                <button class="tw-w-[100px] align-items-center d-flex btn btn-primary btn-md py-1 timegenie_wizard_button" type="button"
                    hx-get="{% url 'timegenie_load_drawer' %}" 
                    hx-vals = '{"drawer_type":"track_new", "view_id": "{{view_filter.view.id}}"}'
                    hx-target="#timegenie-drawer-content" 

                    style="border-radius: 0.475rem 0 0 0.475rem;">
                    <span class="svg-icon svg-icon-4">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect opacity="0.3" x="2" y="2" width="20" height="20" rx="10" fill="currentColor"/>
                            <rect x="10.8891" y="17.8033" width="12" height="2" rx="1" transform="rotate(-90 10.8891 17.8033)" fill="currentColor"/>
                            <rect x="6.01041" y="10.9247" width="12" height="2" rx="1" fill="currentColor"/>
                        </svg>
                    </span>
            
                    <span class="fs-7 ps-1 fw-bolder w-85px">
                        {% if LANGUAGE_CODE == 'ja'%}
                        新規
                        {% else %}
                        New
                        {% endif %}
                    </span>
                </button>   
                
                <button type="button" 
                    class="tw-w-[30px] align-items-center d-flex btn btn-primary btn-md dropdown-toggle py-1 tw-pl-2 dropdown-toggle-split" 
                    data-bs-toggle="dropdown" 
                    aria-expanded="false"
                    style="border-radius: 0 0.475rem 0.475rem 0;border-left: 1px solid #dee2e6;"
                >
                    <span class="svg-icon svg-icon-4">
                        <svg width=24 height=24 xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" fill="currentColor">
                            <path d="M137.4 374.6c12.5 12.5 32.8 12.5 45.3 0l128-128c9.2-9.2 11.9-22.9 6.9-34.9s-16.6-19.8-29.6-19.8L32 192c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9l128 128z"/>
                        </svg>
                    </span>
                </button>
                <ul class="dropdown-menu tw-max-w-[180px] tw-max-h-[300px] tw-overflow-y-scroll">
                    <li>
                        <button class="dropdown-item tw-text-ellipsis tw-overflow-hidden timegenie_wizard_button" type="button"
                            hx-get="{% url 'timegenie_load_drawer' %}" 
                            hx-vals = '{"drawer_type":"bulk-creation", "view_id": "{{view_filter.view.id}}"}'
                            hx-target="#timegenie-drawer-content"
                            hx-indicator=".loading-drawer-spinner"
                            style="border-radius: 0.475rem 0 0 0.475rem;"
                            >
                            {% if LANGUAGE_CODE == 'ja'%}
                                一括作成
                            {% else %}
                                Bulk Create 
                            {% endif %}
                        </button>
                    </li>
                </ul>
            </div>
        </div>
    </div>
    {% if permission == 'hide' %}
        {% include 'data/static/no-access-page.html' %}
    {% else %}
    <div class="{% include "data/utility/table-container.html" %}">
        {% comment %} Views {% endcomment %}
        <div class="{% include "data/utility/tab-pane.html" %}" role="tabpanel" style="z-index:4 !important">
            <div class="{% include "data/utility/table-nav.html" %}">
                <div class="d-flex align-items-end justify-content-between w-100" id="conversation-view-container">
                    <div class="w-100">
                        <div class="d-flex align-items-center">
                            <div class="{% include "data/utility/view-menu-nav-item.html" %}">
                                <a class="{% include "data/utility/view-menu-default.html" %}" 
                                    type="button"
                                    href = "{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}">
                                    <span class="svg-icon svg-icon-muted svg-icon-2"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M22 12C22 17.5 17.5 22 12 22C6.5 22 2 17.5 2 12C2 6.5 6.5 2 12 2C17.5 2 22 6.5 22 12ZM12 6C8.7 6 6 8.7 6 12C6 15.3 8.7 18 12 18C15.3 18 18 15.3 18 12C18 8.7 15.3 6 12 6Z" fill="currentColor"/>
                                        </svg>
                                    </span>
                                </a>
                                {% if not view_filter.view.title %}
                                <div class="w-20px nav-item justify-content-center d-flex fs-6 text-gray-900">
                                    <button type="button" class="{% include "data/utility/view-plus-link.html" %} update-view-settings-button"
                                        hx-vals='{"module": "{{menu_key}}", "page":"timegenie", "type":"update_view", "view_id":"{{view_filter.view.id}}"}'
                                        hx-get="{% url 'timegenie_view_drawer' %}" 
                                        hx-target="#manage-update-view-settings-drawer"
                                        hx-trigger="click"
                                        hx-swap="innerHTML">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-three-dots-vertical" viewBox="0 0 16 16">
                                            <path d="M9.5 13a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z"/>
                                        </svg>
                                    </button>
                                </div>
                                {% endif %}
                            </div>

                            {% comment %} Put Items here {% endcomment %}
                            {% include 'data/projects/partial-view-menu.html' %}

                            <div class="nav-item fs-6 text-gray-900">
                                <button type="button" class="{% include "data/utility/view-plus-link.html" %} create-view-settings-button" 
                                    hx-vals='{"page": "timegenie", "type":"create_view"}'
                                    hx-get="{% url 'timegenie_view_drawer' %}" 
                                    hx-target="#manage-view-settings-drawer"
                                    hx-trigger="click"
                                    hx-swap="innerHTML">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-plus" viewBox="0 0 16 16">
                                        <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="d-flex w-50 justify-content-end">
                        <div class="justify-content-end">
                            <div class="{% include "data/utility/search-wrapper.html" %} hover-tooltip">
                                <span class="search-wrapper-tooltip hover-tooltip-text">
                                    {% if LANGUAGE_CODE == 'ja' %}検索{% else %}Search{% endif %}
                                </span>
                                <div class="d-flex align-items-center">
                                    <form id="filter-form-search" method="get" class="w-100">
                                        <div class="d-flex mb-0 position-relative" style="height: 26px;">
                                            <span class="svg-icon svg-icon-3 search-icon-view" onclick="toggleSearch()">
                                                <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M13 13L10.1 10.1M11.6667 6.33333C11.6667 9.27885 9.27885 11.6667 6.33333 11.6667C3.38781 11.6667 1 9.27885 1 6.33333C1 3.38781 3.38781 1 6.33333 1C9.27885 1 11.6667 3.38781 11.6667 6.33333Z" stroke="#8E8C95" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                                </svg>
                                            </span>
                                            <input
                                            id="base-search-input" type="text" name="q" class="form-control bg-white ps-12 tw-rounded-lg"
                                            value={% if search_q %} "{{ search_q }}" {% else %}""{% endif %}
                                            placeholder={% if LANGUAGE_CODE == 'ja' %} "検索" {% else %} "Search" {% endif %}
                                            onkeypress="if (event.keyCode == 13)document.forms['filter-form-search'].submit();"
                                            >
                                            {% if view_id %}<input type="hidden" value="{{view_id}}" name="view_id">{% endif %}
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="thread-bulk-action-container" class="tw-hidden">
                    <span class="me-10">
                        <button class="py-1 rounded-1 btn btn-sm btn-light fw-bold mt-2 mb-1" onclick="selectAllRecords()">
                            <span>
                                {% if LANGUAGE_CODE == 'ja'%}
                                すべて選択
                                {% else %}
                                Select All
                                {% endif %}
                            </span>
                        </button>
                        <button class="py-1 rounded-1 btn btn-sm btn-light fw-bold mt-2 mb-1" onclick="deselectAllRecords()">
                            <span>
                                {% if LANGUAGE_CODE == 'ja'%}
                                選択を解除
                                {% else %}
                                Deselect All
                                {% endif %}
                            </span>
                        </button>
        
                        <script>
                            function selectAllRecords() {            
                                $('.check_input').prop('checked', true);
                            }
                            function deselectAllRecords() {        
                                $('.check_input').prop('checked', false);
                                document.getElementById('conversation-view-container').classList.remove('tw-hidden')
                                document.getElementById('thread-bulk-action-container').classList.add('tw-hidden')
                            }
                        </script>
                    </span>
                    <button class="btn btn-sm btn-light-success py-1 rounded-1 fw-bold mt-2 mb-1" name="bulk_type" value="active" type="submit" form="bulk-update-attendance">
                        {% if LANGUAGE_CODE == 'ja'%}
                        有効化
                        {% else %}
                        Activate
                        {% endif %}
                    </button>
                    <button class="btn btn-sm btn-light-danger py-1 rounded-1 fw-bold mt-2 mb-1" name="bulk_type" value="archive" type="submit" form="bulk-update-attendance">
                        {% if LANGUAGE_CODE == 'ja'%}
                        アーカイブ
                        {% else %}
                        Archive 
                        {% endif %}
                    </button>

                    <form id="bulk-update-attendance" method="POST" action="{% host_url 'bulk_attendance' host 'app' %}"  enctype="multipart/form-data">
                        {% csrf_token %}
                    </form>
                    <script>
                        document.body.addEventListener("hideTaskBulkActionBtn", function(evt){
                            document.getElementById('task-bulk-action-container').classList.add('d-none')
                            document.getElementById('task-view-contianer').classList.remove('d-none')
                        })
                    </script>
                </div>
            </div>
        </div>
        {% comment %} End of Views {% endcomment %}
    </div>


    {% if page_type == 'calendar' %}
        <div class="mb-5 px-10 w-100">
            <div id='calendar'></div>
        </div>

        <script>
            var app_slug = 'timegenie'
            {% if app %}
            app_slug = "{{app.slug}}"
            {% endif %}
            var language = '{{LANGUAGE_CODE|safe}}'
        
            if (document.getElementById('calendar')){

                var host = window.location.host

                var calendarEl = document.getElementById('calendar');
                var calendar = new FullCalendar.Calendar(calendarEl, {
                    //editable: true,
                    dayMaxEvents: 4,
                    locale: "en",
                    timeZone: 'UTC',
                
                    eventSources: [
                    
                        // your event source
                        {
                        url: "{% host_url 'timegenie_calendar' host 'app' %}",
                        method: 'POST',
                        extraParams: {
                            csrfmiddlewaretoken: "{{ csrf_token }}",
                            type: 'posted'
                        },
                        failure: function() {
                            alert('there was an error while fetching events!');
                        },
                        color: '#4565ed',   // a non-ajax option
                        textColor: 'white' // a non-ajax option
                        }
                    ],
                    eventDidMount: function(info) {

                        //Bold title Text
                        let title_elem = info.el.querySelector('.fc-event-title-container');
                        title_elem.style.fontWeight=500;
            
                        info.el.style.cursor="pointer";
            
            
                    },

                
                    eventClick: function(info) {
                        //request GET
                        if (language =='ja'){window.location.href = "//"+host+"/ja/"+app_slug+"/?track_id="+info.event.extendedProps.track_id}
                        else {window.location.href = "//"+host+"/"+app_slug+"/?track_id="+info.event.extendedProps.track_id}
                    },

                }); 

                calendar.setOption("locale", language)
                calendar.render();

            }
        </script>
    {% else %}
        {% include 'data/timegenie/timegenie-partial-table.html'%}
    {% endif %}

{% if track_id %}
<a id="selected_show_tracks" class="d-none text-center mb-0 text-dark text-hover-primary fw-bolder cursor-pointer manage_full_wizard_button" 
    hx-get="{% url 'timegenie_load_drawer' %}" 
    hx-vals = '{"drawer_type":"track_manage", "track_id":"{{track_id}}", "view_id": "{{view_id}}"}'
    hx-target="#manage-full-drawer-content" 
    hx-trigger="click"
></a>
<script>
    $(document).ready(function() {
        setTimeout(function() {
            document.getElementById('selected_show_tracks').click();
        }, 0);
    });
</script>
{% endif %}

    
<script>
    
    var index_1 = '';
    var index_2 = '';
    const checking_checkbox = (elem,event) => {
        if (event.shiftKey) {
            check_inputs = document.getElementsByClassName('check_input')
            
            index_2 = elem;

            //Do Check
            var pos_1 = '';
            var pos_2 = '';
            for (var i = 0; i < check_inputs.length; i++) {
                if (index_1 == check_inputs[i]){
                    pos_1 = i;
                }
                else if (index_2 == check_inputs[i]){
                    pos_2 = i;
                }
            }
            
            if (pos_1 > pos_2){
                for (var i = pos_2; i < pos_1; i++) {
                    check_inputs[i].checked = true;
                }
            }
            else {
                for (var i = pos_1; i < pos_2; i++) {
                    console.log(i);
                    check_inputs[i].checked = true;
                    
                }
            }
        }
        else{
            if (elem.checked) {
                index_1 = elem;
                document.getElementById('conversation-view-container').classList.add('tw-hidden')
                document.getElementById('thread-bulk-action-container').classList.remove('tw-hidden')
            } else {
                taskSelections = document.querySelectorAll('input[type=checkbox]')
                for (let i = 0; i < taskSelections.length; i++) {
                    const element = taskSelections[i];
                    console.log(element.checked)
                    if (element.checked) {
                        return
                    }
                }
                document.getElementById('conversation-view-container').classList.remove('tw-hidden')
                document.getElementById('thread-bulk-action-container').classList.add('tw-hidden')
            }
        }
    }
</script>
</div>

{% include 'data/javascript/toggleSearch.html' %} 

{% endif %}

{% endblock %}


