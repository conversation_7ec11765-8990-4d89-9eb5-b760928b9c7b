{% extends 'base.html' %}
{% load i18n %}
{% load tz %}
{% load custom_tags %}
{% load humanize %}
{% load hosts %}
{% block content %}
{% get_current_language as LANGUAGE_CODE %}

{% include "data/common/advance_search/advance-search-style.html" %}

<style>
    table {
        width: auto;
    }

    td, th {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .dataTables_scrollHead {
        position: sticky !important;
        top: 0 !important; /* Override DataTables default */
        z-index: 3;
        background-color: white;
    }

</style>

{% include 'data/static/tootip-search-wrapper.html' %}
<div class="d-flex d-flex align-items-center" style="background-color: #FAFAFA !important;">
    {% include 'data/common/module-header-tabs.html' %}
    <div class="w-50 d-flex justify-content-end tw-mr-5" id="view-container">
        {% if permission|check_permission:'edit' %}
            <div class="{% include "data/utility/table-button.html" %}">
                <button id='view-sync-items-action-drawer' type="button" class="max-md:tw-hidden w-125px align-items-center d-flex svg-icon-gray-600 btn py-1 rounded-1 tw-bg-gray-200 hover:tw-bg-gray-300 justify-content-center px-1 object-action-drawer-button"
                    hx-get="{% url 'shopturbo_load_drawer' %}" 
                    hx-trigger="click"
                    onclick="fillActionOrderIds(this),check_permission_action(event, 'edit')"
                    hx-target="#object-action-drawer-content"
                    hx-indicator=".loading-drawer-spinner,.view-form"
                    >
                    <span class="svg-icon svg-icon-4">
                        <svg width="14" height="16" viewBox="0 0 14 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M8.49968 1.33301H4.66264C4.54298 1.33301 4.48316 1.33301 4.43034 1.35123C4.38363 1.36734 4.34109 1.39363 4.30579 1.4282C4.26587 1.4673 4.23912 1.52081 4.18561 1.62783L1.38561 7.22783C1.25782 7.4834 1.19393 7.61119 1.20927 7.71506C1.22268 7.80576 1.27285 7.88694 1.34798 7.93949C1.43403 7.99967 1.5769 7.99967 1.86264 7.99967H5.99968L3.99968 14.6663L12.1284 6.23655C12.4027 5.95214 12.5398 5.80994 12.5478 5.68826C12.5548 5.58265 12.5112 5.48 12.4303 5.4117C12.3371 5.33301 12.1396 5.33301 11.7445 5.33301H6.99968L8.49968 1.33301Z" stroke="#3B3B3F" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </span>
                    <span class="">
                        {% if LANGUAGE_CODE == 'ja'%}
                        アクション
                        {% else %}
                        Action
                        {% endif %}
                    </span>
                </button>
            </div>

            <script>
                {% if open_drawer == 'action_drawer_history' %}
                    $(document).ready(function() {
                        setTimeout(function() {
                            document.getElementById('view-sync-items-action-drawer').click()
                        }, 1000)
                    })
                {% endif %}
                function fillActionOrderIds(elm) {
                    // Now set the hx-vals attribute with the updated order IDs
                    elm.setAttribute('hx-vals', 'js:{"drawer_type":"shopturbo-view-sync-orders", "section": "action_history", "page": "orders","action_tab":"action","module":"{{menu_key}}", "order_ids": getSelectedOrders(), "open_drawer": "{{open_drawer}}"}');
                }
            </script>
        {% endif %}
        
        <div class="{% include "data/utility/header-action-button.html" %}">
            <button class="btn tw-font-[500] tw-rounded-l-lg tw-rounded-r-none tw-border-0 tw-bg-gray-200 hover:tw-bg-gray-300 max-md:tw-hidden w-125px align-items-center d-flex svg-icon-gray-600 py-1 justify-content-center px-1 create-view-settings-button" type="button"
                {% comment %} hx-vals='{"drawer_type":"shopturbo-view-settings","page": "orders", "view_id":"{{view_filter.view.id}}", "download_view": true}' {% endcomment %}
                hx-get="{% url 'shopturbo_load_drawer' %}" 
                hx-trigger="click"
                onclick="fillOrderExportIds(this)"
                hx-target="#manage-contacts-view-settings-drawer"
                hx-indicator=".loading-drawer-spinner,.view-form"
                >
                <span class="svg-icon svg-icon-4">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-upload" viewBox="0 0 16 16">
                        <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5"/>
                        <path d="M7.646 1.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 2.707V11.5a.5.5 0 0 1-1 0V2.707L5.354 4.854a.5.5 0 1 1-.708-.708z"/>
                    </svg>
                </span>
                <span class="">
                    {% if LANGUAGE_CODE == 'ja'%}
                    エクスポート
                    {% else %}
                    Export
                    {% endif %}
                </span>
            </button>
            <button id='view-sync-items' type="button" class="{% include "data/utility/import-button.html" %}"
                hx-get="{% url 'shopturbo_load_drawer' %}" 
                hx-trigger="click"
                onclick="fillOrderIds(this)"
                hx-target="#manage-contacts-view-settings-drawer"
                hx-indicator=".loading-drawer-spinner,.view-form"
                >
                <span class="svg-icon svg-icon-4">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-download" viewBox="0 0 16 16">
                        <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5"/>
                        <path d="M7.646 11.854a.5.5 0 0 0 .708 0l3-3a.5.5 0 0 0-.708-.708L8.5 10.293V1.5a.5.5 0 0 0-1 0v8.793L5.354 8.146a.5.5 0 1 0-.708.708z"/>
                    </svg>
                </span>
                <span class="">
                    {% if LANGUAGE_CODE == 'ja'%}
                    インポート
                    {% else %}
                    Import
                    {% endif %}
                </span>
            </button>
        </div>
        <script>
            function fillOrderExportIds(elm) {
                // Call your JavaScript function to generate the account IDs dynamically
                var checkboxes = document.querySelectorAll('input[type="checkbox"]');
                var checkedIds = [];
                checkboxes.forEach(function(checkbox) {
                    if (checkbox.checked) {
                        checkedIds.push(checkbox.value);
                    }
                });
                var orderIds = checkedIds;
                orderIds = orderIds.filter(id => id !== 'on');
                // Now set the hx-vals attribute with the updated account IDs
                elm.setAttribute('hx-vals', '{"drawer_type":"shopturbo-view-sync-orders","page": "orders","import_export_type":"export", "order_ids":"' + orderIds + '"}');
            }
        </script>

        {% if permission|check_permission:'edit' %}
        <div class="{% include "data/utility/table-button.html" %}">
            <button id='view-sync-items' type="button" class="max-md:tw-block tw-hidden tw-w-[28px] tw-h-[26px] tw-justify-center tw-items-center tw-border-0 tw-bg-transparent tw-mb-2 tw-cursor-pointer create-view-settings-button hover-tooltip"
                hx-get="{% url 'shopturbo_load_drawer' %}" 
                hx-trigger="click"
                onclick="fillOrderIds(this)"
                hx-target="#manage-contacts-view-settings-drawer"
                hx-indicator=".loading-drawer-spinner,.view-form"
                >
                <span class="search-wrapper-tooltip hover-tooltip-text">
                    {% if LANGUAGE_CODE == 'ja' %}同期{% else %}Sync{% endif %}
                </span>
                <span class="tw-flex svg-icon svg-icon-3">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M14.5 20.7259C14.6 21.2259 14.2 21.826 13.7 21.926C13.2 22.026 12.6 22.0259 12.1 22.0259C9.5 22.0259 6.9 21.0259 5 19.1259C1.4 15.5259 1.09998 9.72592 4.29998 5.82592L5.70001 7.22595C3.30001 10.3259 3.59999 14.8259 6.39999 17.7259C8.19999 19.5259 10.8 20.426 13.4 19.926C13.9 19.826 14.4 20.2259 14.5 20.7259ZM18.4 16.8259L19.8 18.2259C22.9 14.3259 22.7 8.52593 19 4.92593C16.7 2.62593 13.5 1.62594 10.3 2.12594C9.79998 2.22594 9.4 2.72595 9.5 3.22595C9.6 3.72595 10.1 4.12594 10.6 4.02594C13.1 3.62594 15.7 4.42595 17.6 6.22595C20.5 9.22595 20.7 13.7259 18.4 16.8259Z" fill="currentColor"/>
                        <path opacity="0.3" d="M2 3.62592H7C7.6 3.62592 8 4.02592 8 4.62592V9.62589L2 3.62592ZM16 14.4259V19.4259C16 20.0259 16.4 20.4259 17 20.4259H22L16 14.4259Z" fill="currentColor"/>
                    </svg>
                </span>
            </button>
            <script>
                function fillOrderIds(elm) {
                    // Call your JavaScript function to generate the account IDs dynamically
                    var checkboxes = document.querySelectorAll('input[type="checkbox"]');
                    var checkedIds = [];
                    checkboxes.forEach(function(checkbox) {
                        if (checkbox.checked) {
                            checkedIds.push(checkbox.value);
                        }
                    });
                    var orderIds = checkedIds;
                    orderIds = orderIds.filter(id => id !== 'on');
                    // Now set the hx-vals attribute with the updated account IDs
                    elm.setAttribute('hx-vals', '{"drawer_type":"shopturbo-view-sync-orders","page": "orders","import_export_type":"import", "order_ids":"' + orderIds + '"}');
                }
            </script>
        </div>
        <div class="btn-group tw-h-[32px]">
            <button class="tw-w-[100px] align-items-center d-flex btn btn-primary btn-md shopturbo-create-wizard-button py-1 view_form_trigger" type="button"
                hx-get="{% url 'load_create_order_drawer' %}" 
                hx-vals = '{"drawer_type":"orders", "view_id": "{{view_filter.view.id}}", "module":"{{menu_key}}","set_id":"{{set_id}}"}'
                hx-target="#shopturbo-create-drawer-content"
                hx-indicator=".loading-drawer-spinner,#shopturbo-create-drawer-content" 
                
                hx-on="htmx:beforeSend: 
                    var create_drawer = document.getElementById('manage-full-drawer-content');
                    if (create_drawer){
                        create_drawer.innerHTML = '';
                    };
                "
                style="border-radius: 0.475rem 0 0 0.475rem;"
                >
                <span class="svg-icon svg-icon-4">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M8.00065 3.33301V12.6663M3.33398 7.99967H12.6673" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </span>
        
                <span class="fs-7 ps-1 fw-bolder w-85px">
                    {% if LANGUAGE_CODE == 'ja'%}
                    新規
                    {% else %}
                    New
                    {% endif %}
                </span>
            </button> 
            <button type="button" 
                class="tw-w-[30px] align-items-center d-flex btn btn-primary btn-md dropdown-toggle py-1 tw-pl-2 dropdown-toggle-split" 
                data-bs-toggle="dropdown" 
                aria-expanded="false"
                style="border-radius: 0 0.475rem 0.475rem 0;border-left: 1px solid #dee2e6;"
            >
                <span class="svg-icon svg-icon-4">
                    <svg width=24 height=24 xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" fill="currentColor">
                        <path d="M137.4 374.6c12.5 12.5 32.8 12.5 45.3 0l128-128c9.2-9.2 11.9-22.9 6.9-34.9s-16.6-19.8-29.6-19.8L32 192c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9l128 128z"/>
                    </svg>
                </span>
            </button>
            <ul class="dropdown-menu tw-max-w-[180px] tw-max-h-[300px] tw-overflow-y-scroll">
                {% for set in property_sets %}
                <li>
                    <button class="dropdown-item tw-text-ellipsis tw-overflow-hidden shopturbo-manage-wizard-button" type="button"
                        hx-get="{% url 'load_create_order_drawer' %}" 
                        hx-vals = '{"drawer_type":"orders", "view_id": "{{view_id}}", "set_id": "{{set.id}}"}'
                        hx-target="#shopturbo-create-drawer-content"
                        hx-indicator=".loading-drawer-spinner,#shopturbo-create-drawer-content" 
                        style="border-radius: 0.475rem 0 0 0.475rem;"
                        >
                        {% if set.name %}
                            {{ set.name}}
                        {% else %}
                            {% if LANGUAGE_CODE == 'ja' %}デフォルト{% else %}Default{% endif %} 
                        {% endif %}
                    </button>
                </li>
                {% endfor %}
            </ul>  
        </div>
        {% endif %}
    </div>
</div>

{% if permission == 'hide' %}
    {% include 'data/static/no-access-page.html' %}
{% else %}
<div class="w-100 tw-pl-0">
    {% include 'data/common/permission-action-warning-message.html' %}
    {% comment %} Views Part {% endcomment %}
    <div class="{% include "data/utility/tab-pane.html" %}" role="tabpanel" style="z-index:4 !important;">
        <div class="{% include "data/utility/table-nav.html" %}">
            <div class="d-flex align-items-end justify-content-between w-100" id="view-container" >
                
                <div class="max-md:tw-hidden w-75 d-flex align-items-center">
                    <div class="nav-item fs-6 text-gray-900">
                        <div class="d-flex align-items-center justify-content-center">
                            <button type="button" class="align-items-center justify-content-center d-flex btn text-gray-600 bg-transparent text-hover-primary tw-px-[5px] create-view-settings-button mb-2 tw-w-[30px]"
                                style="height: 26px;"
                                hx-vals='{"drawer_type":"shopturbo-view-settings","page": "orders"}'
                                hx-get="{% url 'shopturbo_load_drawer' %}" 
                                hx-indicator=".loading-drawer-spinner,.view-form"
                                hx-target="#manage-contacts-view-settings-drawer"
                                hx-swap="innerHTML"
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-plus" viewBox="0 0 16 16">
                                    <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                    
                    {% include "data/projects/partial-dropdown-view-menu.html" %}
                    <div class="{% include "data/utility/view-menu-nav-item.html" %}">
                        <a class="{% include "data/utility/view-menu-default.html" %}" 
                            type="button"
                            href = "{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}">
                            {% comment %} <span class="svg-icon svg-icon-muted svg-icon-2"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M22 12C22 17.5 17.5 22 12 22C6.5 22 2 17.5 2 12C2 6.5 6.5 2 12 2C17.5 2 22 6.5 22 12ZM12 6C8.7 6 6 8.7 6 12C6 15.3 8.7 18 12 18C15.3 18 18 15.3 18 12C18 8.7 15.3 6 12 6Z" fill="currentColor"/>
                                </svg>
                            </span> {% endcomment %}
                            {% if LANGUAGE_CODE == 'ja'%}
                            ビュー
                            {% else %}
                            View
                            {% endif %}
                        </a>
                        {% if not view_filter.view.title %}
                        <div class="w-20px nav-item justify-content-center d-flex fs-6 text-gray-900">
                            <button type="button" class="{% include "data/utility/view-plus-link.html" %} create-view-settings-button"
                                hx-vals='{"module": "{{menu_key}}", "drawer_type":"shopturbo-view-settings","page": "orders", "view_id":"{{view_filter.view.id}}"}'
                                hx-get="{% url 'shopturbo_load_drawer' %}" 
                                hx-indicator=".loading-drawer-spinner,.view-form"
                                hx-target="#manage-contacts-view-settings-drawer"
                                hx-trigger="click"
                                hx-swap="innerHTML"
                                >
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-three-dots-vertical" viewBox="0 0 16 16">
                                    <path d="M9.5 13a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z"/>
                                </svg>
                            </button>
                        </div>
                        {% endif %}
                    </div>

                    {% include 'data/projects/partial-view-menu.html' %}

                </div>

                {% comment %} Mobile View {% endcomment %}
                <div class="tw-hidden max-md:tw-flex tw-w-1/2">
                    <div class="d-flex align-items-center">
                        <!-- Example split danger button -->
                        <div class="btn-group mb-2">
                            <button type="button" class="tw-max-w-[90px] align-items-center d-flex btn bg-white border btn-md tw-px-[10px] create-view-settings-button"
                                style="height: 26px; border-radius: 0.475rem 0 0 0.475rem;"
                                hx-vals='{"drawer_type":"shopturbo-view-settings","page": "orders", "view_id":"{{view_filter.view.id}}"}'
                                hx-get="{% url 'shopturbo_load_drawer' %}" 
                                hx-target="#manage-contacts-view-settings-drawer"
                                hx-indicator=".loading-drawer-spinner,.view-form"
                                hx-trigger="click"
                                hx-swap="innerHTML"
                            >
                                <span class="fs-7 ps-1 fw-bolder tw-text-ellipsis tw-overflow-hidden tw-text-nowrap">
                                    {% if view_filter.view.title %}
                                        {{ view_filter.view.title }}
                                    {% else %}
                                        {% if LANGUAGE_CODE == 'ja'%}ビュー{% else %}View{% endif %}
                                    {% endif %}
                                </span>
                            </button>
                            <button type="button" 
                                class="tw-w-[30px] align-items-center d-flex btn btn-white border btn-md  dropdown-toggle py-1 tw-pl-2 dropdown-toggle-split" 
                                data-bs-toggle="dropdown" 
                                aria-expanded="false"
                                style="height: 26px; border-radius: 0 0.475rem 0.475rem 0;"
                            >
                                <span class="svg-icon svg-icon-4">
                                    <svg width=24 height=24 xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" fill="currentColor">
                                        <path d="M137.4 374.6c12.5 12.5 32.8 12.5 45.3 0l128-128c9.2-9.2 11.9-22.9 6.9-34.9s-16.6-19.8-29.6-19.8L32 192c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9l128 128z"/>
                                    </svg>
                                </span>
                            </button>
                            <ul class="dropdown-menu tw-max-w-[180px] tw-max-h-[300px] tw-overflow-y-scroll">
                                <li><a class="dropdown-item tw-text-ellipsis tw-overflow-hidden" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}">{% if LANGUAGE_CODE == 'ja' %}デフォルト{% else %}Default View{% endif %}</a></li>
                                <div class="dropdown-divider"></div>
                                {% for view in views %}
                                    {% if view.title %}
                                        <li><a class="dropdown-item tw-text-ellipsis tw-overflow-hidden" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?view_id={{view.id}}">{{view.title}}</a></li>
                                    {% endif %}
                                {% endfor %}
                            </ul>
                        </div>
                        <div class="fs-6 text-gray-900 mb-2">
                            <button type="button" class="tw-border-0 tw-bg-transparent tw-text-gray-900 create-view-settings-button" 
                                hx-vals='{"drawer_type":"shopturbo-view-settings","page": "orders"}'
                                hx-get="{% url 'shopturbo_load_drawer' %}" 
                                hx-target="#manage-contacts-view-settings-drawer"
                                hx-indicator=".loading-drawer-spinner,.view-form"
                                hx-swap="innerHTML">
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-plus" viewBox="0 0 16 16">
                                    <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="d-flex w-50 justify-content-end" id="view-container-1">
                    <div class="{% include "data/utility/search-container.html" %}">
                        <div class="{% include "data/utility/search-wrapper.html" %}">
                            <div class="d-flex align-items-center">
                                <form id="filter-form-search" method="get" class="w-100">
                                    <div class="d-flex mb-0 position-relative align-items-center" style="height: 32px">
                                        <span class="svg-icon svg-icon-3 search-icon-view">
                                            <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M13 13L10.1 10.1M11.6667 6.33333C11.6667 9.27885 9.27885 11.6667 6.33333 11.6667C3.38781 11.6667 1 9.27885 1 6.33333C1 3.38781 3.38781 1 6.33333 1C9.27885 1 11.6667 3.38781 11.6667 6.33333Z" stroke="#8E8C95" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </span>
                                        <input
                                        id="base-search-input" type="text" name="q" class="form-control bg-white ps-12 pe-16 h-25px tw-rounded-lg" 
                                        value="{% if advance_search_filter %}{% for k, v in advance_search_filter.items %}{% if k|search_custom_field_object_orders:request %}{% with channel_column=k|search_custom_field_object_orders:request %}{{channel_column.name}}{% endwith %}{% elif k|split:'|'|length > 1 %}{% with column_display=k|split:'|'|search_custom_field_object_order_customer:request %}{{column_display.name}}{% endwith %}{% else %}{% with column_display=k|display_column_orders:request %}{{column_display}}{% endwith %}{% endif %} {% translate_lang v.key|display_filter_rule:LANGUAGE_CODE LANGUAGE_CODE %} {{ v.value }}{% if not forloop.last %}, {% endif %}{% endfor %}{% elif search_q %}{{ search_q }}{% else %}{% endif %}"
                                        placeholder={% if LANGUAGE_CODE == 'ja' %} "検索" {% else %} "Search" {% endif %}
                                        onkeypress="if (event.keyCode == 13)document.forms['filter-form-search'].submit();"
                                        >
                                        {% if request.GET.status == 'archived' %}
                                        <input type="hidden" value="archived" name="status">
                                        {% endif %}
                                        <span class="svg-icon svg-icon-muted svg-icon-3 filter-icon filter-drawer-btn"
                                            hx-get="{% url 'advance_search_drawer' %}"
                                            hx-vals='{"object_type": "{{object_type}}", "module": "{{menu_key}}", "search_scope":"True", "view_id":"{{view_id}}"}'
                                            hx-indicator=".loading-drawer-spinner"
                                            hx-target="#filter-drawer-content"
                                            hx-trigger="click"
                                            hx-swap="innerHTML"
                                        >
                                            <svg width="14" height="12" viewBox="0 0 14 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M1 3.33301L9 3.33301M9 3.33301C9 4.43758 9.89543 5.33301 11 5.33301C12.1046 5.33301 13 4.43758 13 3.33301C13 2.22844 12.1046 1.33301 11 1.33301C9.89543 1.33301 9 2.22844 9 3.33301ZM5 8.66634L13 8.66634M5 8.66634C5 9.77091 4.10457 10.6663 3 10.6663C1.89543 10.6663 1 9.77091 1 8.66634C1 7.56177 1.89543 6.66634 3 6.66634C4.10457 6.66634 5 7.56177 5 8.66634Z" stroke="#8E8C95" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </span>
                                        <span class="svg-icon {% if advance_search.is_active %}svg-icon-primary{%else%}svg-icon-muted{% endif %} svg-icon-3 filter-icon filter-drawer-btn me-7"
                                            hx-get="{% url 'advance_search_drawer' %}"
                                            hx-vals='{"object_type": "{{object_type}}", "module": "{{menu_key}}", "view_id":"{{view_id}}"}'
                                            hx-indicator=".loading-drawer-spinner"
                                            hx-target="#filter-drawer-content"
                                            hx-trigger="click"
                                            hx-swap="innerHTML"
                                        >
                                            <svg width="14" height="10" viewBox="0 0 14 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M3 5H11M1 1H13M5 9H9" stroke="#8E8C95" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </span>
                                        {% if view_id %}<input type="hidden" value="{{view_id}}" name="view_id">{% endif %}
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="{% include "data/utility/view-menu-search.html" %}">
                        <button 
                            onclick="openSearch()"
                            class="tw-w-[28px] tw-h-[26px] tw-justify-center tw-items-center tw-border-0 tw-bg-transparent tw-mb-2 tw-cursor-pointer hover-tooltip" type="button"
                            >
                            <span class="search-wrapper-tooltip hover-tooltip-text">
                                {% if LANGUAGE_CODE == 'ja' %}検索{% else %}Search{% endif %}
                            </span>
                            <span class="tw-flex svg-icon svg-icon-3">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                    <path d="M21.7 18.9L18.6 15.8C17.9 16.9 16.9 17.9 15.8 18.6L18.9 21.7C19.3 22.1 19.9 22.1 20.3 21.7L21.7 20.3C22.1 19.9 22.1 19.3 21.7 18.9Z" fill="black" />
                                    <path opacity="0.3" d="M11 20C6 20 2 16 2 11C2 6 6 2 11 2C16 2 20 6 20 11C20 16 16 20 11 20ZM11 4C7.1 4 4 7.1 4 11C4 14.9 7.1 18 11 18C14.9 18 18 14.9 18 11C18 7.1 14.9 4 11 4ZM8 11C8 9.3 9.3 8 11 8C11.6 8 12 7.6 12 7C12 6.4 11.6 6 11 6C8.2 6 6 8.2 6 11C6 11.6 6.4 12 7 12C7.6 12 8 11.6 8 11Z" fill="black" />
                                </svg>
                            </span>
                        </button>
                    </div>
                </div>
            </div>

            <div id="view-header-container" class="tw-hidden w-100">
                <div class="justify-content-between align-items-center flex-row d-flex">
                    <div class="w-100">
                        {% include 'data/common/select-all-in-view-button.html' %} 
                        
                        {% if permission|check_permission:'edit' %}
                        <button class="py-1 rounded-1 btn btn-sm btn-light-primary fw-bold mt-2 mb-1" onclick="bulk_check_toggle(),check_permission_action(event, 'edit', 'edit_bulk_modal')">
                            <span>
                                {% if LANGUAGE_CODE == 'ja'%}
                                編集
                                {% else %}
                                Edit
                                {% endif %}
                            </span>
                        </button>
                        
                        <button class="py-1 rounded-1 btn btn-sm btn-light-warning fw-bold mt-2 mb-1" onclick="check_permission_action(event, 'edit')" name="bulk_duplicate" type="submit" form="order-form">
                            {% if LANGUAGE_CODE == 'ja'%}
                            複製
                            {% else %}
                            Duplicate
                            {% endif %}
                        </button>
                        {% endif %}
                        {% if permission|check_permission:'archive' %}
                        <button class="py-1 rounded-1 btn btn-sm btn-light-success fw-bold mt-2 mb-1" onclick="check_permission_action(event, 'archive', 'manage_restore_bulk')">
                            {% if LANGUAGE_CODE == 'ja'%}
                            有効化
                            {% else %}
                            Activate
                            {% endif %}
                        </button>
                        <button class="btn btn-sm btn-light-danger py-1 rounded-1 fw-bold mt-2 mb-1" onclick="check_permission_action(event, 'archive', 'manage_delete_bulk')">
                            {% if LANGUAGE_CODE == 'ja'%}
                            アーカイブ
                            {% else %}
                            Archive 
                            {% endif %}
                        </button>
                        {% endif %}
                        <script>
                            document.body.addEventListener("hideTaskBulkActionBtn", function(evt){
                                document.getElementById('view-header-container').classList.add('d-none')
                                document.getElementById('order-view-contianer').classList.remove('d-none')
                            })
                        </script>
                        <script>
                            function getSelectedOrders() {
                                var selectedOrders = [];
                                var classNameElements = document.getElementsByClassName("order-selection");
                                if (classNameElements){
                                    classNameElements.forEach(function(classNameElement) {
                                        if (classNameElement.checked) {
                                            selectedOrders.push(classNameElement.value);
                                        }
                                    });  
                                }
                                return selectedOrders;
                            }
                        </script>
                    </div>
                    <div class="d-flex">
                        {% if permission|check_permission:'edit' %}
                            <button id='view-sync-items' type="button" class="{% include "data/utility/gray-header-button.html" %} create-view-settings-button"
                                hx-get="{% url 'shopturbo_load_drawer' %}" 
                                hx-trigger="click"
                                onclick="fillActionOrderIds(this),check_permission_action(event, 'edit')"
                                hx-target="#manage-contacts-view-settings-drawer"
                                hx-indicator=".loading-drawer-spinner,.view-form"
                                >
                                <span class="svg-icon svg-icon-4">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-magic" viewBox="0 0 16 16">
                                        <path d="M9.5 2.672a.5.5 0 1 0 1 0V.843a.5.5 0 0 0-1 0zm4.5.035A.5.5 0 0 0 13.293 2L12 3.293a.5.5 0 1 0 .707.707zM7.293 4A.5.5 0 1 0 8 3.293L6.707 2A.5.5 0 0 0 6 2.707zm-.621 2.5a.5.5 0 1 0 0-1H4.843a.5.5 0 1 0 0 1zm8.485 0a.5.5 0 1 0 0-1h-1.829a.5.5 0 0 0 0 1zM13.293 10A.5.5 0 1 0 14 9.293L12.707 8a.5.5 0 1 0-.707.707zM9.5 11.157a.5.5 0 0 0 1 0V9.328a.5.5 0 0 0-1 0zm1.854-5.097a.5.5 0 0 0 0-.706l-.708-.708a.5.5 0 0 0-.707 0L8.646 5.94a.5.5 0 0 0 0 .707l.708.708a.5.5 0 0 0 .707 0l1.293-1.293Zm-3 3a.5.5 0 0 0 0-.706l-.708-.708a.5.5 0 0 0-.707 0L.646 13.94a.5.5 0 0 0 0 .707l.708.708a.5.5 0 0 0 .707 0z"/>
                                    </svg>
                                </span>
                                <span class="">
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    アクション
                                    {% else %}
                                    Action
                                    {% endif %}
                                </span>
                            </button>
                            <div class="{% include "data/utility/table-button.html" %}">
                                <button id='view-sync-items' type="button" class="w-150px align-items-center d-flex justify-content-center svg-icon-gray-600 btn btn-sm py-1 rounded-1 bg-gray-100 create-view-settings-button"
                                    style="width: 130px"
                                    hx-get="{% url 'shopturbo_load_drawer' %}" 
                                    hx-trigger="click"
                                    onclick="fillOrderExportIds(this)"
                                    hx-target="#manage-contacts-view-settings-drawer"
                                    hx-indicator=".loading-drawer-spinner,.view-form"
                                    >
                                    <span class="svg-icon svg-icon-4">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-upload" viewBox="0 0 16 16">
                                            <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5"/>
                                            <path d="M7.646 1.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 2.707V11.5a.5.5 0 0 1-1 0V2.707L5.354 4.854a.5.5 0 1 1-.708-.708z"/>
                                        </svg>
                                    </span>
                                    <span class="">
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        エクスポート
                                        {% else %}
                                        Export
                                        {% endif %}
                                    </span>
                                </button>
                            </div>
                        {% endif %}
                    </div>
                </div>
                {% include 'data/common/select-all-in-view-record-msg.html' %} 
            </div>
        </div>
    </div>

    <div class="{% if search_q %}max-md:tw-flex {% else %}max-md:tw-hidden {% endif %}tw-hidden tw-items-center tw-py-2 border-bottom border-bottom-1 tw-transition-all tw-duration-300" id="search-bar">
        <form id="filter-form-search" method="get" class="w-100">
            <div class="d-flex mb-0 position-relative align-items-center" style="height: 26px;">
                <span class="svg-icon svg-icon-3 search-icon-view">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M21.7 18.9L18.6 15.8C17.9 16.9 16.9 17.9 15.8 18.6L18.9 21.7C19.3 22.1 19.9 22.1 20.3 21.7L21.7 20.3C22.1 19.9 22.1 19.3 21.7 18.9Z" fill="black" />
                        <path opacity="0.3" d="M11 20C6 20 2 16 2 11C2 6 6 2 11 2C16 2 20 6 20 11C20 16 16 20 11 20ZM11 4C7.1 4 4 7.1 4 11C4 14.9 7.1 18 11 18C14.9 18 18 14.9 18 11C18 7.1 14.9 4 11 4ZM8 11C8 9.3 9.3 8 11 8C11.6 8 12 7.6 12 7C12 6.4 11.6 6 11 6C8.2 6 6 8.2 6 11C6 11.6 6.4 12 7 12C7.6 12 8 11.6 8 11Z" fill="black" />
                    </svg>
                </span>
                <input id="base-search-input" type="text" name="q" class="form-control bg-white ps-12 pe-16"
                value="{% if advance_search_filter %}{% for k, v in advance_search_filter.items %}{% if k|search_custom_field_object_orders:request %}{% with channel_column=k|search_custom_field_object_orders:request %}{{channel_column.name}}{% endwith %}{% elif k|split:'|'|length > 1 %}{% with column_display=k|split:'|'|search_custom_field_object_order_customer:request %}{{column_display.name}}{% endwith %}{% else %}{% with column_display=k|display_column_orders:request %}{{column_display}}{% endwith %}{% endif %} {% translate_lang v.key|display_filter_rule:LANGUAGE_CODE LANGUAGE_CODE %} {{ v.value }}{% if not forloop.last %}, {% endif %}{% endfor %}{% elif search_q %}{{ search_q }}{% else %}{% endif %}"             
                placeholder={% if LANGUAGE_CODE == 'ja' %} "検索" {% else %} "Search" {% endif %}
                onkeypress="if (event.keyCode == 13)document.forms['filter-form-search'].submit();"
                >
                {% if request.GET.status == 'archived' %}
                <input type="hidden" value="archived" name="status">
                {% endif %}
                <span class="svg-icon svg-icon-muted svg-icon-3 filter-icon filter-drawer-btn me-7"
                    hx-get="{% url 'advance_search_drawer' %}"
                    hx-vals='{"object_type": "{{object_type}}", "module": "{{menu_key}}", "search_scope":"True"}'
                    hx-indicator=".loading-drawer-spinner"
                    hx-target="#filter-drawer-content"
                    hx-trigger="click"
                    hx-swap="innerHTML"
                >
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path fill="black" d="M19.43 12.98c.04-.32.07-.65.07-.98s-.03-.66-.07-.98l2.11-1.65a.5.5 0 0 0 .12-.63l-2-3.46a.5.5 0 0 0-.6-.22l-2.49 1a7.03 7.03 0 0 0-1.7-.98l-.38-2.65A.5.5 0 0 0 14 2h-4a.5.5 0 0 0-.5.45l-.38 2.65c-.63.25-1.21.57-1.74.98l-2.49-1a.5.5 0 0 0-.6.22l-2 3.46a.5.5 0 0 0 .12.63L4.57 11c-.04.32-.07.65-.07.98s.03.66.07.98l-2.11 1.65a.5.5 0 0 0-.12.63l2 3.46a.5.5 0 0 0 .6.22l2.49-1c.53.41 1.11.73 1.74.98l.38 2.65c.03.24.24.45.5.45h4c.26 0 .47-.21.5-.45l.38-2.65c.63-.25 1.21-.57 1.74-.98l2.49 1a.5.5 0 0 0 .6-.22l2-3.46a.5.5 0 0 0-.12-.63l-2.11-1.65zM12 15.5A3.5 3.5 0 1 1 12 8a3.5 3.5 0 0 1 0 7.5z"/>
                    </svg>
                </span>
                <span class="svg-icon svg-icon-muted svg-icon-3 filter-icon filter-drawer-btn me-7"
                    hx-get="{% url 'advance_search_drawer' %}"
                    hx-vals='{"object_type": "{{object_type}}", "module": "{{menu_key}}", "search_scope":"True", "view_id":"{{view_id}}"}'
                    hx-indicator=".loading-drawer-spinner"
                    hx-target="#filter-drawer-content"
                    hx-trigger="click"
                    hx-swap="innerHTML"
                >
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path fill="black" d="M19.43 12.98c.04-.32.07-.65.07-.98s-.03-.66-.07-.98l2.11-1.65a.5.5 0 0 0 .12-.63l-2-3.46a.5.5 0 0 0-.6-.22l-2.49 1a7.03 7.03 0 0 0-1.7-.98l-.38-2.65A.5.5 0 0 0 14 2h-4a.5.5 0 0 0-.5.45l-.38 2.65c-.63.25-1.21.57-1.74.98l-2.49-1a.5.5 0 0 0-.6.22l-2 3.46a.5.5 0 0 0 .12.63L4.57 11c-.04.32-.07.65-.07.98s.03.66.07.98l-2.11 1.65a.5.5 0 0 0-.12.63l2 3.46a.5.5 0 0 0 .6.22l2.49-1c.53.41 1.11.73 1.74.98l.38 2.65c.03.24.24.45.5.45h4c.26 0 .47-.21.5-.45l.38-2.65c.63-.25 1.21-.57 1.74-.98l2.49 1a.5.5 0 0 0 .6-.22l2-3.46a.5.5 0 0 0-.12-.63l-2.11-1.65zM12 15.5A3.5 3.5 0 1 1 12 8a3.5 3.5 0 0 1 0 7.5z"/>
                    </svg>
                </span>
                <span class="svg-icon {% if advance_search.is_active %}svg-icon-primary{%else%}svg-icon-muted{% endif %} svg-icon-3 filter-icon filter-drawer-btn"
                    hx-get="{% url 'advance_search_drawer' %}"
                    hx-vals='{"object_type": "{{object_type}}", "module": "{{menu_key}}", "view_id":"{{view_id}}"}'
                    hx-indicator=".loading-drawer-spinner"
                    hx-target="#filter-drawer-content"
                    hx-trigger="click"
                    hx-swap="innerHTML"
                >
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M21 7H3C2.4 7 2 6.6 2 6V4C2 3.4 2.4 3 3 3H21C21.6 3 22 3.4 22 4V6C22 6.6 21.6 7 21 7Z" fill="currentColor"/>
                        <path opacity="0.3" d="M21 14H3C2.4 14 2 13.6 2 13V11C2 10.4 2.4 10 3 10H21C21.6 10 22 10.4 22 11V13C22 13.6 21.6 14 21 14ZM22 20V18C22 17.4 21.6 17 21 17H3C2.4 17 2 17.4 2 18V20C2 20.6 2.4 21 3 21H21C21.6 21 22 20.6 22 20Z" fill="currentColor"/>
                    </svg>
                </span>
                <input type="hidden" value="{{view_id}}" name="view_id">
            </div>
        </form>
    </div>

    <div class="mb-2 d-none" id="d-select-additional-options">
        <div class="status bg-light-dark text-center pt-4 pb-3 px-5 rounded">
            {% if LANGUAGE_CODE == 'ja'%}
            このページのすべてのレコードが選択されました。 
            {% else %}
            All records on this page are selected. 
            {% endif %}
             <a onclick="toggleText()" 
                class="btn btn-dark" 
                data-bs-toggle="collapse" 
                id="select-additional-options-toggle" 
                role="button" 
                aria-expanded="false" 
                aria-controls="collapseExample">
                {% if LANGUAGE_CODE == 'ja'%}
                全てのレコードを選択する 
                {% else %}
                Select all records. 
                {% endif %}
    
            </a>
        </div>
    </div>

    <div class="d-flex flex-column flex-lg-row">
        <div class="flex-lg-row-fluid">
            <form method="POST" id="order-form">
                {% csrf_token %}

                {% if shopturbo_orders_columns %}
                <input type="hidden" name="shopturbo_orders_columns" value="{{shopturbo_orders_columns}}" />
                {% endif %}
                

                <div class="pt-0 table-responsive" style="max-height: 75vh;">
                    {% if not config_view or config_view != 'kanban' %}
                    <table class="{% include "data/utility/table.html" %} orders-table">
                        <thead class="{% include "data/utility/table-header.html" %} position-sticky">
                            <tr class="align-middle border-bottom border-bottom-1">

                                {% for shopturbo_orders_column in shopturbo_orders_columns %}
                                <th {% if shopturbo_orders_column == 'checkbox' %} 
                                    class="{% include "data/utility/column-checkbox-size.html" %}" 
                                    {% elif shopturbo_orders_column == 'order_id' %}
                                    class="{% include "data/utility/column-id-size.html" %}"
                                    {% else %} 
                                    class="" 
                                    {% endif %}>
                                        {% if shopturbo_orders_column != 'checkbox' %}
                                            {% if 'shipping_info' in shopturbo_orders_column %}
                                                {% with channel_column=shopturbo_orders_column|split:'|' %}
                                                    {{channel_column.2}} - {{channel_column.3}}
                                                {% endwith %}
                                            {% elif "line_item|" in shopturbo_orders_column|lower %}
                                                {% with channel_column=shopturbo_orders_column|search_custom_field_object_line_item_orders:request %}
                                                    {{channel_column.name}}
                                                {% endwith %} 
                                            {% elif shopturbo_orders_column|startswith:'source_item__' %}
                                                {% with custom_column=shopturbo_orders_column|cut:'source_item__'|search_custom_field_object_items:request item_col_display=shopturbo_orders_column|cut:'source_item__'|display_column_items:request %}
                                                    {% if custom_column %}
                                                        {{'source_item'|display_column_orders:request}} - {{custom_column.name}}
                                                    {% else %}
                                                        {{'source_item'|display_column_orders:request}} - {{item_col_display}}
                                                    {% endif %}
                                                {% endwith %}
                                            {% elif shopturbo_orders_column|search_custom_field_object_orders:request %}
                                                {% with channel_column=shopturbo_orders_column|search_custom_field_object_orders:request %}
                                                    {{channel_column.name|display_column_orders:request}}
                                                {% endwith %}
                                            {% elif shopturbo_orders_column|search_channel_objects:request %}
                                                {% with channel_column=shopturbo_orders_column|search_channel_objects:request %}
                                                    {{channel_column}}
                                                {% endwith %}
                                            {% elif shopturbo_orders_column|split:'|'|length > 1 and 'order_platform|' not in shopturbo_orders_column %}
                                                {% with channel_column=shopturbo_orders_column|split:'|'|search_custom_field_object_order_customer:request %}
                                                    {{channel_column.name}}
                                                {% endwith %}
                                            {% elif shopturbo_orders_column|startswith:'item__' %}
                                                {% with column_value=shopturbo_orders_column|cut:'item__'|display_column_items:request %}
                                                    {{'source_item'|display_column_orders:request}} - {{column_value}}
                                                {% endwith %}
                                            {% else %}
                                                {% with shopturbo_orders_column=shopturbo_orders_column|display_column_orders:request %}
                                                    {{shopturbo_orders_column}}
                                                {% endwith %}
                                            {% endif %}
                                        {% endif %}
                                    </th>
                                    {% if shopturbo_orders_column == 'order_id' %} 
                                    <th class="" style="width: 20px;">
                                    </th>
                                    {% endif %}
                                {% endfor %}
                            </tr>
                        </thead>

                        <tbody class="fs-6">
                            {% for order in shopturbo_orders %}
                                {% if config_view == 'item_list' %}
                                    {% with items_=order.shopturboitemsorders_set.all %}
                                        {% if items_%}
                                            {% for item_ in items_  %}
                                                <tr id="row-{{item_.id}}" 
                                                    hx-get="{% host_url 'shopturbo_order_row_detail' order.id host 'app' %}" 
                                                    hx-vals='{"view_id": "{{view_filter.view.id}}", "decimal_point": "{{decimal_point.id}}", "selected_order_id": "{{selected_order_id}}", "page": "{{page}}", "item_id": "{{item_.id}}", "module":"{{menu_key}}"}'
                                                    hx-trigger="load" 
                                                    hx-indicator=".row_load-{{item_.id}}">
                                                    <td class="d-flex justify-content-center w-100">
                                                        <style>
                                                            /* Styles for the spinner */
                                                            .row_load-{{item_.id}} {
                                                                display: none; /* Initially hidden */
                                                            }
                                                            .htmx-request .row_load-{{item_.id}},
                                                            .htmx-request.row_load-{{item_.id}} {
                                                                display: inline-block; /* Display during htmx request */
                                                            }
                                                        </style>
                                                        <!-- Spinner icon -->
                                                        <span class="spinner-border spinner-border-lg text-secondary row_load-{{item_.id}}" role="status">
                                                            <span class="visually-hidden">Loading...</span>
                                                        </span>
                                                    </td>
                                                </tr>
                                            {% endfor %}
                                        {% else %}
                                            <tr id="row-{{order.id}}" 
                                                hx-get="{% host_url 'shopturbo_order_row_detail' order.id host 'app' %}" 
                                                hx-vals='{"view_id": "{{view_filter.view.id}}", "decimal_point": "{{decimal_point.id}}", "selected_order_id": "{{selected_order_id}}", "page": "{{page}}", "module":"{{menu_key}}"}'
                                                hx-trigger="load" 
                                                hx-indicator=".row_load-{{order.id}}">
                                                <td class="d-flex justify-content-center w-100">
                                                    <style>
                                                        /* Styles for the spinner */
                                                        .row_load-{{order.id}} {
                                                            display: none; /* Initially hidden */
                                                        }
                                                        .htmx-request .row_load-{{order.id}},
                                                        .htmx-request.row_load-{{order.id}} {
                                                            display: inline-block; /* Display during htmx request */
                                                        }
                                                    </style>
                                                    <!-- Spinner icon -->
                                                    <span class="spinner-border spinner-border-lg text-secondary row_load-{{order.id}}" role="status">
                                                        <span class="visually-hidden">Loading...</span>
                                                    </span>
                                                </td>
                                            </tr>
                                        {% endif %}
                                    {% endwith %}
                                {% else %}
                                    <tr id="row-{{order.id}}" 
                                        hx-get="{% host_url 'shopturbo_order_row_detail' order.id host 'app' %}" 
                                        hx-vals='{"view_id": "{{view_filter.view.id}}", "decimal_point": "{{decimal_point.id}}", "selected_order_id": "{{selected_order_id}}", "page": "{{page}}", "module":"{{menu_key}}"}'
                                        hx-trigger="load" 
                                        hx-indicator=".row_load-{{order.id}}">
                                        <td class="d-flex justify-content-center w-100">
                                            <style>
                                                /* Styles for the spinner */
                                                .row_load-{{order.id}} {
                                                    display: none; /* Initially hidden */
                                                }
                                                .htmx-request .row_load-{{order.id}},
                                                .htmx-request.row_load-{{order.id}} {
                                                    display: inline-block; /* Display during htmx request */
                                                }
                                            </style>
                                            <!-- Spinner icon -->
                                            <span class="spinner-border spinner-border-lg text-secondary row_load-{{order.id}}" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </span>
                                        </td>
                                    </tr>
                                {% endif %}
                            {% endfor %}
                        </tbody>
                    </table>
                    {% elif config_view == 'kanban' %}
                        {% if view_filter.choice_customfield %}
                            {% read_custom_field view_filter.choice_customfield "order" request as customfieldname %}
                                {% with choice_values=customfieldname|read_choice_value_viewfilter:view_filter %}
                                
                                <div id="kanban-order" class="mt-5"></div>

                                <script>
                                    
                                //Merge the data
                                var kanbandata = []
                                
                                {% for choice_value in choice_values %}
                                    kanbandata.push({
                                        'id': "{{choice_value.value}}",
                                        'title': "{{choice_value.label|display_column_KANBAN:request}}",
                                        'item':[]
                                    })
                                {% endfor %}
                            

                                // Class definition
                                var KTJKanbanDemoBasic = function() {
                                    // Private functions
                                    var exampleBasic = function() {
                                        var kanban = new jKanban({
                                            element: '#kanban-order',
                                            gutter: '0',
                                            widthBoard: '250px',
                                            dragBoards: false,  
                                            boards: kanbandata,
                                            dragEl: function(el, source) {
                                                // Handle scrolling based on element position

                                                var container = document.querySelector('.flex-lg-row-fluid');
                                                var containerRect = container.getBoundingClientRect();
                                                
                                                // Add mousemove event listener during drag
                                                function handleMouseMove(e) {
                                                    var threshold = 100; // pixels from edge to trigger scroll
                                                    
                                                    // If mouse is near right edge
                                                    if (e.clientX > containerRect.right - threshold) {
                                                        document.querySelector('.kanban-container').scrollBy({
                                                            left: 100,
                                                            behavior: 'smooth'
                                                        });
                                                    }
                                                    
                                                    // If mouse is near left edge
                                                    if (e.clientX < containerRect.left + threshold) {
                                                        document.querySelector('.kanban-container').scrollBy({
                                                            left: -100,
                                                            behavior: 'smooth'
                                                        });
                                                    }


                                                    // Vertical scrolling
                                                    // Bottom edge
                                                    if (e.clientY > containerRect.bottom - threshold) {
                                                        container.scrollBy({
                                                            top: 100,
                                                            behavior: 'smooth'
                                                        });
                                                    }
                                                    // Top edge
                                                    if (e.clientY < containerRect.top + threshold) {
                                                        container.scrollBy({
                                                            top: -100,
                                                            behavior: 'smooth'
                                                        });
                                                    }

                                                    // Get the currently visible viewport height
                                                    var viewportHeight = window.innerHeight;
                                                    // Vertical scrolling
                                                    // For bottom edge - trigger when mouse is near bottom of viewport
                                                    if (e.clientY > viewportHeight - threshold) {
                                                        document.querySelector('#kt_content_container').scrollBy({
                                                            top: 1000,
                                                            behavior: 'smooth'
                                                        });
                                                    }
                                                    
                                                    // For top edge - trigger when mouse is near top of viewport
                                                    if (e.clientY < threshold) {
                                                        document.querySelector('#kt_content_container').scrollBy({
                                                            top: -1000,
                                                            behavior: 'smooth'
                                                        });
                                                    }
                                                }

                                                // Add listener when drag starts
                                                document.addEventListener('mousemove', handleMouseMove);

                                                // Clean up listener when drag ends
                                                document.addEventListener('mouseup', function cleanup() {
                                                    document.removeEventListener('mousemove', handleMouseMove);
                                                    document.removeEventListener('mouseup', cleanup);
                                                });
                                            },
                                            dragendBoard: function (el) {
                                                var boardIds = $('.kanban-board').map(function(){
                                                    return $(this).attr("data-id")
                                                }).get();
                        
                                                var sorted = [];
                                                var currentOrder = 0;
                                                boardIds.forEach(function(value, index, array) {
                                                    sorted.push({
                                                        "id": value,
                                                        "order": currentOrder++
                                                    })
                                                });
                                                
                                                var target_status = JSON.stringify(sorted)
                                                // Send Ajax request
                                                // URL of the API endpoint you want to fetch data from
                                                const apiUrl = "{% host_url 'kanban_shopturbo_board_api' host 'app' %}";
                                                const postData = {
                                                    type: 'orders',
                                                    target_status:target_status,
                                                    view_id:"{{view_filter.id}}"
                                                    };
                                                // Configuring the fetch options for a POST request
                                                const fetchOptions = {
                                                    method: 'POST',
                                                    headers: {
                                                    'Content-Type': 'application/json',
                                                    'X-CSRFToken': "{{ csrf_token }}",
                                                    },
                                                    body: JSON.stringify(postData),
                                                };
                                                // Using the Fetch API to make a GET request
                                                fetch(apiUrl,fetchOptions)
                                                    .then(response => {
                                                    // Check if the request was successful (status code 200-299)
                                                    if (!response.ok) {
                                                        throw new Error(`HTTP error! Status: ${response.status}`);
                                                    }

                                                    // Parse the response as JSON
                                                    return response.json();
                                                    })
                                                    .then(data => {
                                                    // Handle the data received from the API
                                                    // console.log('Data received:', data);
                                                    })
                                                    .catch(error => {
                                                    // Handle errors that may occur during the fetch operation
                                                    console.error('Fetch error:', error);
                                                    });



                                            },
                                            dropEl: function (el, target, source, sibling) {
                                                var deal_id = el.querySelector('span').id;
                                                var target_status = target.parentElement.getAttribute('data-id')

                                                 // Get all items in the target column
                                                var items = target.querySelectorAll('.kanban-item');
                                                var newOrder = Array.from(items).map(item => item.querySelector('span').id);

                                                // Send Ajax request
                                                // URL of the API endpoint you want to fetch data from
                                                const apiUrl = "{% host_url 'kanban_shopturbo_status_api' host 'app' %}";
                                                const postData = {
                                                    id:deal_id,
                                                    type: 'orders',
                                                    target_status:target_status,
                                                    view_id:"{{view_filter.id}}",
                                                    newOrder:newOrder
                                                    };
                                                // Configuring the fetch options for a POST request
                                                const fetchOptions = {
                                                    method: 'POST',
                                                    headers: {
                                                    'Content-Type': 'application/json',
                                                    'X-CSRFToken': "{{ csrf_token }}",
                                                    },
                                                    body: JSON.stringify(postData),
                                                };
                                                // Using the Fetch API to make a GET request
                                                fetch(apiUrl,fetchOptions)
                                                    .then(response => {
                                                    // Check if the request was successful (status code 200-299)
                                                    if (!response.ok) {
                                                        throw new Error(`HTTP error! Status: ${response.status}`);
                                                    }

                                                    // Parse the response as JSON
                                                    return response.json();
                                                    })
                                                    .then(data => {
                                                    // Handle the data received from the API
                                                    // console.log('Data received:', data);
                                                    })
                                                    .catch(error => {
                                                    // Handle errors that may occur during the fetch operation
                                                    console.error('Fetch error:', error);
                                                    });

                                            },
                                            
                                        });

                                        // Add "Load More" button to each board's container
                                        kanban.options.boards.forEach(function(board) {
                                            addLoadMoreButtonToBoard(kanban, board.id);
                                        });

                                    };

                                    var currentPage={};
                                    // Function to add "Load More" button directly inside the board container
                                    function addLoadMoreButtonToBoard(kanban, boardStatus) {
                                        var boardIDAttr = '[data-id="' + boardStatus + '"]'
                                        var boardElement = document.querySelector(boardIDAttr);
                                        boardElement.style.display = 'flex';
                                        boardElement.style.flexDirection = 'column';
                                        boardElement.style.height = '100%'; // Make sure the board takes full height
                                        boardElement.style.justifyContent = 'space-between'; // Distribute items to keep the button at the bottom
                                        boardElement.style.minHeight = '0%'
                                        
                                        currentPage[boardStatus] = 1;  // Initialize the page number for this board

                                        var boardItemStr=`
                                        <div style="display: flex; justify-content: center; margin-top: auto;">
                                            <button id="load-more-btn-${boardStatus}" 
                                                    style="width: 100%; padding: 5px; background-color: #00bcd4; color: white; border: none; border-radius: 4px; text-align: center; font-weight: bold;"
                                                    hx-get="${window.location.href}" 
                                                    hx-vals='{ "boardStatus": "${boardStatus}", "page": "${currentPage[boardStatus]}" }' 
                                                    hx-target="[data-id='${boardStatus}'] .kanban-drag"
                                                    hx-swap="beforeend" ,
                                                    hx-trigger="load,click"
                                                    >
                                                
                                            {% if LANGUAGE_CODE == 'ja'%}もっと見る{% else %}Load More{% endif %}

                                            </button>
                                        </div>`

                                        boardElement.insertAdjacentHTML('beforeend', boardItemStr);

                                        // Use HTMX event to update the page number after the content is loaded
                                        document.getElementById('load-more-btn-' + boardStatus).addEventListener('htmx:afterRequest', function() {
                                            var responseHTML = event.detail.xhr.responseText;
                                            console.log("event.detail: ", event.detail)
                                            if (!responseHTML.trim()) {
                                                this.style.display = 'none';
                                            } else {
                                                currentPage[boardStatus]++;  // Increment the page number for this specific board after the request is made
                                                this.setAttribute('hx-vals', JSON.stringify({ boardStatus: boardStatus, page: currentPage[boardStatus] }));
                                            
                                                // Check if there are more pages to load
                                                var parser = new DOMParser();
                                                var doc = parser.parseFromString(responseHTML, 'text/html');
                                                var kanbanItems = doc.getElementsByClassName('kanban-' + boardStatus);
                                                var lastKanbanItem = kanbanItems[kanbanItems.length - 1];
                                                var hasNext = lastKanbanItem ? lastKanbanItem.getAttribute('data-has-next') : 'false';
                                                if (hasNext === 'false') {
                                                    this.style.display = 'none';
                                                }
                                            
                                            }
                                        });
                                    }

                                
                                    return {
                                        // Public Functions
                                        init: function() {
                                            exampleBasic();
                                        }
                                    };
                                }();

                                KTJKanbanDemoBasic.init();


                                {% if not view_filter.kanban_unlisted %}
                                    var board = document.querySelector('div[data-id="unlisted"]');
                                    // Example: Hide the board
                                    if (board) {
                                        board.style.display = 'none';
                                    }
                                {% endif %}
                                
    
                                </script>
                                <style>
                                    .kanban-container{
                                        flex-wrap: nowrap !important;
                                        overflow-x: auto !important;
                                    }
                                </style> 

                                {% endwith %}
                             
                        {% endif %}

                    {% endif %}


                    <input name='flag_all' id='flag_all' class="flag_all" hidden type="checkbox" ></input>
                    {% if view_id %}
                    <input type="hidden" value="{{view_id}}" name="view_id">
                    {% endif %}

                    {% if permission|check_permission:'edit' %}
                        <div id='modal-load'
                            hx-vals='{"object_type": "{{object_type}}","module": "{{menu_key}}","view_id": "{{view_id}}"}'
                            hx-get="{% url 'get_bulk_update_properties' %}" 
                            hx-trigger='load'
                            hx-target="this">
                        </div>
                    {% endif %}
                    <div class="modal fade" tabindex="-1" id="manage_delete_bulk">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header pb-0 border-0 justify-content-end">
                                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                                    </div>
                                </div>
                                <div class="modal-body pb-0">
                                    <div class="mb-13 text-center">
                                        <h3 class="modal-title">
                                            
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            一括アーカイブの確認
                                            {% else %}
                                            Bulk Archive Confirmation
                                            {% endif %}
                                        
                                        </h3>
                                    </div>
                                    <div class="border-bottom">
                                        <div class="fv-rowd-flex flex-column mb-8">
                                            <label class="{% include 'data/utility/form-label.html' %}">
                                                <span class="">
                                                    {% if LANGUAGE_CODE == 'ja'%}
                                                    選択されたレコードをアーカイブしてもよろしいですか?
                                                    {% else %}
                                                    Are you sure to archive selected records?
                                                    {% endif %}
                                                </span>
                                            </label>
                                    
                                        </div>
                                    </div>
                                </div>

                
                                <div class="modal-footer border-0">
                                    <button name="bulk_delete_orders" type="submit" class="btn btn-danger">
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        アーカイブ
                                        {% else %}
                                        Archive
                                        {% endif %}
                                    </button>
                                    <a data-bs-dismiss="modal" class="btn bg-gray-200 border">
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        キャンセル
                                        {% else %}
                                        Cancel
                                        {% endif %}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div> 

                    <div class="modal fade" tabindex="-1" id="manage_restore_bulk">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header pb-0 border-0 justify-content-end">
                                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                                    </div>
                                </div>
                                <div class="modal-body pb-0">
                                    <div class="mb-13 text-center">
                                        <h3 class="modal-title">
                                            
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            一括有効化の確認
                                            {% else %}
                                            Bulk Activate Confirmation
                                            {% endif %}
                                        
                                        </h3>
                                    </div>
                                    <div class="border-bottom">
                                        <div class="fv-rowd-flex flex-column mb-8">
                                            <label class="{% include 'data/utility/form-label.html' %}">
                                                <span class="">
                                                    {% if LANGUAGE_CODE == 'ja'%}
                                                    これらの受注レコードを有効化してもよろしいですか?
                                                    {% else %}
                                                    Are you sure to activate these orders?
                                                    {% endif %}
                                                </span>
                                            </label>
                                    
                                        </div>
                                    </div>
                                </div>
                                
                
                                <div class="modal-footer border-0">
                                    <button name="bulk_restore_orders" type="submit" class="btn btn-success">
                                        
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        有効化
                                        {% else %}
                                        Activate
                                        {% endif %}
                                        
                                    </button>
                                    <a data-bs-dismiss="modal" class="btn bg-gray-200 border">
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        キャンセル
                                        {% else %}
                                        Cancel
                                        {% endif %}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div> 


                </div>
            </form>
            

            {% if config_view != 'kanban' %}
            <div class="{% include "data/utility/pagination.html" %}">
                 {% if LANGUAGE_CODE == 'ja'%}
                    {{paginator_item_begin}}–{{paginator_item_end}} の {{paginator.count}} 件
                {% else %}
                    Viewing {{paginator_item_begin}}–{{paginator_item_end}} of {{paginator.count}} results
                {% endif %}

                <div class="ms-5">
                    {% if page_content.has_previous %}     
                        <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300  w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page=1&{% query_transform %}">&laquo; {% translate_lang "First" LANGUAGE_CODE %}</a>
                        <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300  w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page={{ page_content.previous_page_number }}&{% query_transform %}">&lsaquo; {% translate_lang "Previous" LANGUAGE_CODE %}</a>
                    {% endif %}
                            
                    {% if page_content.has_next %}
                        <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300  w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page={{ page_content.next_page_number }}&{% query_transform %}">{% translate_lang "Next" LANGUAGE_CODE %} &rsaquo;</a>
                        <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300  w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page={{ page_content.paginator.num_pages }}&{% query_transform %}">{% translate_lang "Last" LANGUAGE_CODE %} &raquo;</a>
                    {% endif %}
                </div>
            </div>
            {% endif %}

        </div>
    </div>
</div>




{% if order_id and config_view == 'kanban' %}
<a id="selected_show_order" class="d-none shopturbo-manage-wizard-button"
    hx-get="{% host_url 'load_manage_order_drawer' host 'app' %}"
    hx-vals = '{"drawer_type":"manage-orders", "order_id":"{{order_id}}" ,"view_id":"{{view_filter.view.id}}", "page": "{{page}}"}'
    hx-target="#shopturbo-drawer-content"
    hx-indicator=".loading-drawer-spinner,#shopturbo-drawer-content"
    hx-trigger="click"></a>
<script>
    $(document).ready(function() {
        setTimeout(function() {
            document.getElementById('selected_show_order').click();
        }, 0);
    });
</script>
{% endif %}

{% if selected_order_id and selected_order_id != 'None' %}
<a id="selected_order" class="d-none manage-full-wizard-button"
    hx-get="{% host_url 'load_manage_order_drawer' host 'app' %}"
    hx-vals = '{"drawer_type":"manage-orders", "order_id":"{{selected_order_id}}" ,"view_id":"{{view_id}}", "page": "{{page}}", "module": "{{module}}", "side_drawer": "{{side_drawer}}"}'
    hx-target="#manage-full-drawer-content"
    hx-indicator=".loading-drawer-spinner,#manage-full-drawer-content"
    hx-trigger="click"
></a>
<script>
    $(document).ready(function() {
        document.getElementById('selected_order').click();
    });
</script>
{% endif %}

<script>
    // Function to load script dynamically
    function loadScript(url, callback) {
        var script = document.createElement('script');
        script.type = 'text/javascript';
        script.src = url;
        script.onload = callback;
        script.onerror = function() {
            console.error('Failed to load script:', url);
        };
        document.head.appendChild(script);
    }

    // Check if jQuery is loaded, if not, load it
    function ensureJQuery(callback) {
        if (typeof window.jQuery === 'undefined') {
            console.log('jQuery not loaded, loading now...');
            loadScript('https://cdn.jsdelivr.net/jquery/latest/jquery.min.js', function() {
                console.log('jQuery loaded successfully');
                if (callback) callback();
            });
        } else {
            console.log('jQuery already loaded');
            if (callback) callback();
        }
    }

    // Check if DataTable is loaded, if not, load it
    function ensureDataTableScripts(callback) {
        if (typeof $.fn.DataTable === 'undefined') {
            console.log('DataTable not loaded, loading scripts...');
            loadScript('https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js', function() {
                console.log('DataTable core loaded');
                loadScript('https://cdn.datatables.net/fixedcolumns/4.3.0/js/dataTables.fixedColumns.min.js', function() {
                    console.log('DataTable FixedColumns loaded');
                    if (callback) callback();
                });
            });
        } else {
            console.log('DataTable already loaded');
            if (callback) callback();
        }
    }

    function ensureDataTable(callback) {
        // First ensure jQuery is loaded
        ensureJQuery(function() {
            // Then ensure DataTable scripts are loaded
            ensureDataTableScripts(function() {
                if (callback && typeof callback === 'function') {
                    callback();
                }
            });
        });
    }

    function initializeDataTable() {
        var table = $(".orders-table").DataTable({
            scrollX: true,
            scrollY: "75vh",
            scrollCollapse: true,
            autoWidth: true,
            fixedColumns: {
                left: 2
            },
            ordering: false,
            searching: false,
            paging: false,
            info: false,
            language: {
                emptyTable: "{% translate_lang 'No data available in table' LANGUAGE_CODE %}"
            },
            columnDefs: [
                { 
                    targets: '_all',
                    className: 'text-nowrap'
                }
            ]
        });

        // Adjust table width after initialization
        $(window).on('resize', function() {
            table.columns.adjust();
        });

        return table;
    }

    {% if shopturbo_orders %}
    var requestNum = {{shopturbo_orders|length}}
    document.addEventListener('htmx:afterRequest', function(evt) {
        if (requestNum <= 0) return;

        if (evt.target.tagName.toLowerCase() === 'tr') {
            requestNum -= 1;
            if (requestNum <= 0) {
                ensureDataTable(function() {
                    var table = initializeDataTable();
                    
                    // Initialize dropdowns after table is ready
                    const dropdowns = $('.dropdown-toggle', table.cells(null, 1).nodes());
                    dropdowns.each((index, dropdownToggleEl) => {
                        var instance = new bootstrap.Dropdown(dropdownToggleEl, {
                            popperConfig(defaultBsPopperConfig) {
                                return { ...defaultBsPopperConfig, strategy: "fixed" };
                            },
                        });

                        dropdownToggleEl.addEventListener("show.bs.dropdown", function (event) {
                            $(event.target).closest("td").addClass("z-index-3");
                        });

                        dropdownToggleEl.addEventListener("hide.bs.dropdown", function (event) {
                            $(event.target).closest("td").removeClass("z-index-3");
                        });
                    });
                });
            }
        }
    });
    {% else %}
    $(document).ready(function() {
        ensureDataTable(function() {
            initializeDataTable();
        });
    });
    {% endif %}

    function toggleText() {
        var x = document.getElementById("select-additional-options-toggle");
        var toggle_data = x.getAttribute('toggle-data')
        if (toggle_data !== "true") {
            {% if LANGUAGE_CODE == 'ja'%}
            x.innerHTML = "選択を解除";
            {% else %}
            x.innerHTML = "Clear All";
            {% endif %}

          $(".flag_all").each(function(index, element) {
                element.value = true
            });
        
            x.setAttribute('toggle-data',"true")
    

        } else {

            x.setAttribute('toggle-data',"false")

            addcontactelem = document.getElementById("update-order");
            addcontactelem.classList.add("disabled");
   
            $('input[type=checkbox]').prop('checked', false);

            //Hide
            var element_select_options = document.getElementById("d-select-additional-options");
            element_select_options.classList.add("d-none")

            x.innerHTML = "{% if LANGUAGE_CODE == 'ja'%}すべて選択 ({{paginator.count}}) このセクションの連絡先{%else%}Select All ({{paginator.count}}) contacts in this sections{%endif%}";
            
            $(".flag_all").each(function(index, element) {
                element.value = false
            });

        }
      }



    function select_all() {
        $('#selectAll').prop('checked', !$('#selectAll').prop('checked'));
        if ($('#selectAll').prop('checked') == true) {
            addcontactelem = document.getElementById("update-order");
            addcontactelem.classList.add("disabled");
            
            $('input[type=checkbox]').prop('checked', false);

            var element_select_options = document.getElementById("d-select-additional-options");
            element_select_options.classList.add("d-none")

        } else {
            addcontactelem = document.getElementById("update-order");
            addcontactelem.classList.remove("disabled");
            $('input[type=checkbox]').prop('checked', true);

            var element_select_options = document.getElementById("d-select-additional-options");
            element_select_options.classList.remove("d-none")

        }
    }


    var contactChecked = {}
    var first_id_check_box = null;
    var start = moment().subtract(29, 'days');
    var end = moment();
    
    function toTimestamp(strDate){
        var datum = Date.parse(strDate);
        return datum/1000;
     }
    function cb(start, end) {
        startUnix = toTimestamp(start.format("YYYY-MM-DD"))*1000
        endUnix = toTimestamp(end.format("YYYY-MM-DD"))*1000
        queryParam = '?since='+String(startUnix)+'&end='+String(endUnix)
        url = "{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}"+queryParam
        window.location.replace(url)
        
    }

    $("#shopturbo_date_picker").daterangepicker({
        startDate: start,
        endDate: end,
        ranges: {
            "Today": [moment(), moment()],
            "Yesterday": [moment().subtract(1, "days"), moment().subtract(1, "days")],
            "Last 7 Days": [moment().subtract(6, "days"), moment()],
            "Last 30 Days": [moment().subtract(29, "days"), moment()],
            "This Month": [moment().startOf("month"), moment().endOf("month")],
            "Last Month": [moment().subtract(1, "month").startOf("month"), moment().subtract(1, "month").endOf("month")]
        },
        maxDate: new Date()
    }, cb);

    
    function getDrawerURL(){
        return encodeURIComponent(window.location.href.replace(window.location.search, "") + "{{drawer_query_params|safe}}");
    }


</script>

{% include 'data/javascript/toggleSearch.html' %}

<style>
    .loading-spinner{
        display:none;
    }
    .htmx-request .loading-spinner{
        display:inline-block;
    }
    .htmx-request.loading-spinner{
        display:inline-block;
    }
</style>


<script>
    // Keyboard shortcut for opening the 'Create New' drawer (n key)
    document.addEventListener('keydown', function(event) {
        // Ignore if input, textarea, or contenteditable is focused
        const active = document.activeElement;
        if (active && (active.tagName === 'INPUT' || active.tagName === 'TEXTAREA' || active.isContentEditable)) {
            return;
        }

        // Check if the key pressed is 'n'
        if (event.key.toLowerCase() === 'n') {
            event.preventDefault(); // Prevent default 'n' behavior
            
            // Find the 'Create New' button (adjust selector if needed)
            const newButton = document.querySelector('.view_form_trigger'); 
            
            if (newButton) {
                newButton.click(); // Simulate click to open the drawer
            }
        }
    });
</script>


<script>
    function check_permission_action(event, permission_type, ...args){

        let source = args.length > 0 ? args[0] : null;

        const checkInputs = document.querySelectorAll('.check_input:checked');

        let members = "{{group_members}}"
        members = members.split(',')
        const user_id = '{{request.user.id}}'
        const permission = '{{permission}}';
        const permission_list = permission.split('|');
        let scope = ''
        permission_list.forEach(p => {
            if (p.includes(permission_type)) {
                p_split = p.split('_');
                scope = p_split[0]
            }
        })

        let msg = '';
        let denied = false;
        for (let i = 0; i < checkInputs.length; i++) {
            var owner_id = checkInputs[i].dataset.owner;
            if (owner_id){
                if (scope == 'user'){
                    if (owner_id.toString() !== user_id.toString()) {
                        {% if LANGUAGE_CODE == 'ja' %}
                        msg = "操作が拒否されました。自分のアイテムのみ編集または削除することができます。";
                        {% else %}
                        msg = "Action denied. You are only allowed to edit or delete your own items.";
                        {% endif %}                    
                        checkInputs[i].click()
                        denied = true;
                    }
                } else if (scope == 'team'){
                    if (!members.includes(owner_id.toString())) {
                        {% if LANGUAGE_CODE == 'ja' %}
                        msg = "操作が拒否されました。自分または自分のチームに割り当てられたアイテムのみ編集または削除できます。";
                        {% else %}
                        msg = "Action denied. You can only edit or delete items assigned to you or your team.";
                        {% endif %}
                        checkInputs[i].click()
                        denied = true;
                    }
                } 
            }
        }
        if (denied) {
            event.preventDefault();
            event.stopImmediatePropagation();
            document.getElementById('permissionActionWarning').innerHTML = msg;
            setTimeout(() => {
                document.getElementById('permissionActionWarning').innerHTML = '';
            }, 4000);
            msg = ''
        } else if (source){
            const modalEl = document.getElementById(source);
            const modal = bootstrap.Modal.getOrCreateInstance(modalEl);
            modal.show();
        }

    }
</script>

{% endif %}
{% endblock %}