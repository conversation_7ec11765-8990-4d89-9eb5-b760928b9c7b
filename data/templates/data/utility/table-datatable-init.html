{% comment %}
Reusable DataTable initialization script
Usage: {% include "data/utility/table-datatable-init.html" with table_class="orders-table" object_list=objects %}
{% endcomment %}
{% load i18n %}
{% load custom_tags %}

{% get_current_language as LANGUAGE_CODE %}

<script>
    console.log('=== TABLE DATATABLE INIT SCRIPT LOADED ===');
    console.log('Table class:', "{{ table_class|default:'data-table' }}");
    console.log('Object list length:', {{ object_list|length|default:0 }});

    function ensureDataTable(callback) {
        $(document).ready(function() {
            ensureDataTableScripts(function() {
                if (callback && typeof callback === 'function') {
                    callback();
                }
            });
        });
    }

    function initializeDataTable() {
        var tableClass = "{{ table_class|default:'data-table' }}";
        var tableElement = $("." + tableClass);

        // Validate column count before initializing DataTable
        if (tableElement.length > 0) {
            var headerCells = tableElement.find('thead tr:first th').length;
            var firstRowCells = tableElement.find('tbody tr:first td').length;

            if (headerCells !== firstRowCells) {
                console.error('DataTable column mismatch detected:');
                console.error('Header columns:', headerCells);
                console.error('Data columns:', firstRowCells);

                // Debug: Show actual structure
                console.log('Header structure:', tableElement.find('thead tr:first th').map(function() {
                    return $(this).text().trim() || '[empty]';
                }).get());
                console.log('First row structure:', tableElement.find('tbody tr:first td').map(function() {
                    return $(this).text().trim() || '[empty]';
                }).get());

                console.error('Skipping DataTable initialization to prevent error');
                return null;
            }
        }

        var table = tableElement.DataTable({
            scrollX: true,
            scrollY: "75vh",
            scrollCollapse: true,
            autoWidth: true,
            fixedColumns: {
                left: 2
            },
            ordering: false,
            searching: false,
            paging: false,
            info: false,
            language: {
                emptyTable: "{% translate_lang 'No data available in table' LANGUAGE_CODE %}"
            },
            columnDefs: [
                {
                    targets: '_all',
                    className: 'text-nowrap'
                }
            ]
        });

        // Adjust table width after initialization
        $(window).on('resize', function() {
            table.columns.adjust();
        });

        console.log('DataTable initialized successfully');
        return table;
    }

    {% if object_list %}
    var requestNum = {{ object_list|length }}
    document.addEventListener('htmx:afterRequest', function(evt) {
        if (requestNum <= 0) return;

        if (evt.target.tagName.toLowerCase() === 'tr') {
            requestNum -= 1;
            if (requestNum <= 0) {
                // Debug table structure immediately when all rows are loaded
                console.log('=== ALL HTMX REQUESTS COMPLETE - CHECKING TABLE STRUCTURE ===');
                var tableClass = "{{ table_class|default:'data-table' }}";
                var tableElement = $("." + tableClass);

                if (tableElement.length > 0) {
                    var headerCells = tableElement.find('thead tr:first th').length;
                    var firstRowCells = tableElement.find('tbody tr:first td').length;

                    console.log('Table found with class:', tableClass);
                    console.log('Header columns:', headerCells);
                    console.log('First row columns:', firstRowCells);

                    if (headerCells !== firstRowCells) {
                        console.error('COLUMN MISMATCH DETECTED BEFORE DATATABLE INIT!');
                        console.error('Header structure:', tableElement.find('thead tr:first th').map(function() {
                            return $(this).text().trim() || '[empty]';
                        }).get());
                        console.error('First row structure:', tableElement.find('tbody tr:first td').map(function() {
                            return $(this).text().trim() || '[empty]';
                        }).get());

                        // Don't initialize DataTable
                        return;
                    } else {
                        console.log('Column counts match - proceeding with DataTable initialization');
                    }
                } else {
                    console.error('Table not found with class:', tableClass);
                    return;
                }

                ensureDataTable(function() {
                    var table = initializeDataTable();

                    // Initialize dropdowns after table is ready
                    if (table) {
                        const dropdowns = $('.dropdown-toggle', table.cells(null, 1).nodes());
                        dropdowns.each((index, dropdownToggleEl) => {
                            var instance = new bootstrap.Dropdown(dropdownToggleEl, {
                                popperConfig(defaultBsPopperConfig) {
                                    return { ...defaultBsPopperConfig, strategy: "fixed" };
                                },
                            });

                            dropdownToggleEl.addEventListener("show.bs.dropdown", function (event) {
                                $(event.target).closest("td").addClass("z-index-3");
                            });

                            dropdownToggleEl.addEventListener("hide.bs.dropdown", function (event) {
                                $(event.target).closest("td").removeClass("z-index-3");
                            });
                        });
                    }
                });
            }
        }
    });
    {% endif %}
</script>
