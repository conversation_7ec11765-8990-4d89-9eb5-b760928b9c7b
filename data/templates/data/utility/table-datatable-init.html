{% comment %}
Reusable DataTable initialization script
Usage: {% include "data/utility/table-datatable-init.html" with table_class="orders-table" object_list=objects %}
{% endcomment %}
{% load i18n %}
{% load custom_tags %}

{% get_current_language as LANGUAGE_CODE %}

<script>
    function ensureDataTable(callback) {
        $(document).ready(function() {
            ensureDataTableScripts(function() {
                console.log('DataTable scripts loaded, executing callback...');
                if (callback && typeof callback === 'function') {
                    callback();
                }
            });
        });
    }

    function initializeDataTable() {
        var tableElement = $(".{{ table_class|default:"data-table" }}");

        // Debug: Log table structure
        console.log('=== DataTable Debug Info ===');
        console.log('Table class:', '{{ table_class|default:"data-table" }}');
        console.log('Table found:', tableElement.length > 0);

        if (tableElement.length > 0) {
            var headerCells = tableElement.find('thead tr:first th').length;
            var firstRowCells = tableElement.find('tbody tr:first td').length;

            console.log('Header columns count:', headerCells);
            console.log('First data row columns count:', firstRowCells);
            console.log('Header cells:', tableElement.find('thead tr:first th').map(function() { return $(this).text().trim() || '[empty]'; }).get());

            if (headerCells !== firstRowCells) {
                console.error('COLUMN MISMATCH DETECTED!');
                console.error('Headers:', headerCells, 'vs Data:', firstRowCells);

                // Log all rows to identify the issue
                tableElement.find('tbody tr').each(function(index) {
                    var rowCells = $(this).find('td').length;
                    console.log('Row', index + 1, 'has', rowCells, 'cells');
                });

                // Don't initialize DataTable if there's a mismatch
                return null;
            }
        }

        var table = tableElement.DataTable({
            scrollX: true,
            scrollY: "75vh",
            scrollCollapse: true,
            autoWidth: true,
            fixedColumns: {
                left: 2
            },
            ordering: false,
            searching: false,
            paging: false,
            info: false,
            language: {
                emptyTable: "{% translate_lang 'No data available in table' LANGUAGE_CODE %}"
            },
            columnDefs: [
                {
                    targets: '_all',
                    className: 'text-nowrap'
                }
            ]
        });

        // Adjust table width after initialization
        $(window).on('resize', function() {
            table.columns.adjust();
        });

        console.log('DataTable initialized successfully');
        return table;
    }

    {% if object_list %}
    var requestNum = {{ object_list|length }}
    document.addEventListener('htmx:afterRequest', function(evt) {
        if (requestNum <= 0) return;

        if (evt.target.tagName.toLowerCase() === 'tr') {
            requestNum -= 1;
            console.log('HTMX request completed. Remaining:', requestNum);

            if (requestNum <= 0) {
                console.log('All rows loaded, initializing DataTable...');
                // Add a small delay to ensure DOM is fully updated
                setTimeout(function() {
                    // Debug: Check table structure before DataTable initialization
                    var tableElement = $(".{{ table_class|default:"data-table" }}");
                    console.log('=== Pre-DataTable Structure Check ===');
                    console.log('Table found:', tableElement.length > 0);
                    if (tableElement.length > 0) {
                        console.log('Total rows in tbody:', tableElement.find('tbody tr').length);
                        console.log('Header structure:', tableElement.find('thead').html());
                        console.log('First row structure:', tableElement.find('tbody tr:first').html());
                    }

                    ensureDataTable(function() {
                        var table = initializeDataTable();

                    // Only initialize dropdowns if table was successfully created
                    if (table) {
                        console.log('Initializing dropdowns...');
                        // Initialize dropdowns after table is ready
                        const dropdowns = $('.dropdown-toggle', table.cells(null, 1).nodes());
                        dropdowns.each((index, dropdownToggleEl) => {
                            var instance = new bootstrap.Dropdown(dropdownToggleEl, {
                                popperConfig(defaultBsPopperConfig) {
                                    return { ...defaultBsPopperConfig, strategy: "fixed" };
                                },
                            });

                            dropdownToggleEl.addEventListener("show.bs.dropdown", function (event) {
                                $(event.target).closest("td").addClass("z-index-3");
                            });

                            dropdownToggleEl.addEventListener("hide.bs.dropdown", function (event) {
                                $(event.target).closest("td").removeClass("z-index-3");
                            });
                        });
                        console.log('Dropdowns initialized');
                    } else {
                        console.error('DataTable initialization failed - skipping dropdown setup');
                    }
                    });
                }, 100); // 100ms delay to ensure DOM is ready
            }
        }
    });
    {% endif %}
</script>
