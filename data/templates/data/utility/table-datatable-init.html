{% comment %}
Reusable DataTable initialization script
Usage: {% include "data/utility/table-datatable-init.html" with table_class="orders-table" object_list=objects %}
{% endcomment %}
{% load i18n %}
{% load custom_tags %}

{% get_current_language as LANGUAGE_CODE %}

<script>
    function ensureDataTable(callback) {
        $(document).ready(function() {
            ensureDataTableScripts(function() {
                if (callback && typeof callback === 'function') {
                    callback();
                }
            });
        });
    }

    function initializeDataTable() {
        var table = $(".{{ table_class|default:"data-table" }}").DataTable({
            scrollX: true,
            scrollY: "75vh",
            scrollCollapse: true,
            autoWidth: true,
            fixedColumns: {
                left: 2
            },
            ordering: false,
            searching: false,
            paging: false,
            info: false,
            language: {
                emptyTable: "{% translate_lang 'No data available in table' LANGUAGE_CODE %}"
            },
            columnDefs: [
                { 
                    targets: '_all',
                    className: 'text-nowrap'
                }
            ]
        });

        // Adjust table width after initialization
        $(window).on('resize', function() {
            table.columns.adjust();
        });

        return table;
    }

    {% if object_list %}
    var requestNum = {{ object_list|length }}
    document.addEventListener('htmx:afterRequest', function(evt) {
        if (requestNum <= 0) return;

        if (evt.target.tagName.toLowerCase() === 'tr') {
            requestNum -= 1;
            if (requestNum <= 0) {
                ensureDataTable(function() {
                    var table = initializeDataTable();
                    
                    // Initialize dropdowns after table is ready
                    const dropdowns = $('.dropdown-toggle', table.cells(null, 1).nodes());
                    dropdowns.each((index, dropdownToggleEl) => {
                        var instance = new bootstrap.Dropdown(dropdownToggleEl, {
                            popperConfig(defaultBsPopperConfig) {
                                return { ...defaultBsPopperConfig, strategy: "fixed" };
                            },
                        });

                        dropdownToggleEl.addEventListener("show.bs.dropdown", function (event) {
                            $(event.target).closest("td").addClass("z-index-3");
                        });

                        dropdownToggleEl.addEventListener("hide.bs.dropdown", function (event) {
                            $(event.target).closest("td").removeClass("z-index-3");
                        });
                    });
                });
            }
        }
    });
    {% endif %}
</script>
