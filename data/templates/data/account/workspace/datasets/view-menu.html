{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}


<style>
    table {
        width: auto;
    }
    
    td, th {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    
</style>

{% include 'data/static/tootip-search-wrapper.html' %}

<div class="{% include "data/utility/tab-pane.html" %}" role="tabpanel">
    <div class="{% include "data/utility/table-nav.html" %}">
        <div class="{% include "data/utility/table-content.html" %}" id="panel-view-container">
            {% comment %} Desktop {% endcomment %}

        
            <div class="max-md:tw-hidden w-50">
                <div class="d-flex align-items-center">
                    <div class="nav-item fs-6 text-gray-900">
                        <button type="button" class="{% include "data/utility/view-plus-link.html" %} dataset-create-button"
                            hx-get="{% url 'dataset_form' %}" 
                            hx-trigger="click"
                            hx-target="#dataset-drawer-content"
                            hx-indicator=".loading-drawer-spinner,.view-form">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-plus" viewBox="0 0 16 16">
                                <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                            </svg>
                        </button>
                    </div>

                    {% include 'data/projects/partial-dropdown-view-menu.html' %}

                    <div class="
                    nav-item me-2 d-flex align-items-center rounded-0 hover-boder-0 
                    {% if not view_filter.view.title %}
                    active fw-bolder border-end-0 border-bottom border-bottom-3 border-primary
                    {% endif %} 
                    ">
                        <a class="{% include "data/utility/view-menu-default.html" %}" 
                            type="button"
                            href = "{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?object_type={{object_type}}">
                            <span class="svg-icon svg-icon-muted svg-icon-2"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M22 12C22 17.5 17.5 22 12 22C6.5 22 2 17.5 2 12C2 6.5 6.5 2 12 2C17.5 2 22 6.5 22 12ZM12 6C8.7 6 6 8.7 6 12C6 15.3 8.7 18 12 18C15.3 18 18 15.3 18 12C18 8.7 15.3 6 12 6Z" fill="currentColor"/>
                                </svg>
                            </span>
                        </a>
                        {% if not view_filter.view.title %}
                        <div class="w-20px nav-item justify-content-center d-flex fs-6 text-gray-900">
                            <button type="button" class="{% include "data/utility/view-plus-link.html" %} create-panel-button"
                                {% if object_type == 'panels' %}
                                    hx-vals='{"module": "{{menu_key}}", "view_page": "panels", "view_id":"{{view_filter.view.id}}"}'
                                {% else %}
                                    hx-vals='{"module": "{{menu_key}}", "view_page": "dashboards", "view_id":"{{view_filter.view.id}}"}'
                                {% endif%}
                                hx-get="{% url 'report_views_drawer' %}"
                                hx-target="#panel-wizard"
                                hx-trigger="click"
                                hx-swap="innerHTML"
                                hx-indicator="#panel-wizard-loading"
                                >
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-three-dots-vertical" viewBox="0 0 16 16">
                                    <path d="M9.5 13a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z"/>
                                </svg>
                            </button>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            {% comment %} Mobile View {% endcomment %}
            <div class="tw-hidden max-md:tw-flex tw-w-1/2">
                <div class="d-flex align-items-center">
                    <!-- Example split danger button -->
                    <div class="btn-group mb-2">
                        <button type="button" class="tw-max-w-[90px] align-items-center d-flex btn bg-white border btn-md tw-px-[10px] create-panel-button"
                            style="height: 26px; border-radius: 0.475rem 0 0 0.475rem;"
                            {% if object_type == 'panels' %}
                                hx-vals='{"view_page": "panels", "view_id":"{{view_filter.view.id}}"}'
                            {% else %}
                                hx-vals='{"view_page": "dashboards", "view_id":"{{view_filter.view.id}}"}'
                            {% endif%}
                            hx-get="{% url 'report_views_drawer' %}" 
                            hx-target="#panel-wizard"
                            hx-trigger="click"
                            hx-swap="innerHTML"
                        >
                            <span class="fs-7 ps-1 fw-bolder tw-text-ellipsis tw-overflow-hidden tw-text-nowrap">
                                {% if view_filter.view.title %}
                                    {{ view_filter.view.title }}
                                {% else %}
                                    {% if LANGUAGE_CODE == 'ja'%}ビュー{% else %}View{% endif %}
                                {% endif %}
                            </span>
                        </button>
                        <button type="button" 
                            class="tw-w-[30px] align-items-center d-flex btn btn-white border btn-md  dropdown-toggle py-1 tw-pl-2 dropdown-toggle-split" 
                            data-bs-toggle="dropdown" 
                            aria-expanded="false"
                            style="height: 26px; border-radius: 0 0.475rem 0.475rem 0;"
                        >
                            <span class="svg-icon svg-icon-4">
                                <svg width=24 height=24 xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" fill="currentColor">
                                    <path d="M137.4 374.6c12.5 12.5 32.8 12.5 45.3 0l128-128c9.2-9.2 11.9-22.9 6.9-34.9s-16.6-19.8-29.6-19.8L32 192c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9l128 128z"/>
                                </svg>
                            </span>
                        </button>
                        <ul class="dropdown-menu tw-max-w-[180px] tw-max-h-[300px] tw-overflow-y-scroll">
                            {% if object_type == 'panels' %}
                                <li><a class="dropdown-item tw-text-ellipsis tw-overflow-hidden" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?object_type=panels">{% if LANGUAGE_CODE == 'ja' %}デフォルト{% else %}Default View{% endif %}</a></li>
                            {% else %}
                                <li><a class="dropdown-item tw-text-ellipsis tw-overflow-hidden" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?object_type=dashboards">{% if LANGUAGE_CODE == 'ja' %}デフォルト{% else %}Default View{% endif %}</a></li>
                            {% endif %}
                            <div class="dropdown-divider"></div>
                            {% for view in views %}
                                {% if view.title %}
                                    {% if object_type == 'panels' %}
                                        <li><a class="dropdown-item tw-text-ellipsis tw-overflow-hidden" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?object_type=panels&view_id={{view.id}}">{{view.title}}</a></li>
                                    {% else %}
                                        <li><a class="dropdown-item tw-text-ellipsis tw-overflow-hidden" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?object_type=dashboards&view_id={{view.id}}">{{view.title}}</a></li>
                                    {% endif%}
                                {% endif %}
                            {% endfor %}
                        </ul>
                    </div>
                    <div class="fs-6 text-gray-900 mb-2">
                        <button type="button" class="tw-border-0 tw-bg-transparent tw-text-gray-900 create-panel-button" 
                                {% if object_type == 'panels' %}
                                    hx-vals='{"view_page": "panels"}'
                                {% else %}
                                    hx-vals='{"view_page": "dashboards"}'
                                {% endif%}
                                hx-get="{% url 'report_views_drawer' %}"
                                hx-target="#panel-wizard"
                                hx-trigger="click"
                                hx-swap="innerHTML"
                                hx-indicator="#panel-wizard-loading">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-plus" viewBox="0 0 16 16">
                                <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <div class="d-flex w-50 justify-content-end">
                <div class="{% include "data/utility/search-container.html" %}">
                    <div class="{% include "data/utility/search-wrapper.html" %}">
                        <span class="search-wrapper-tooltip hover-tooltip-text">
                            {% if LANGUAGE_CODE == 'ja' %}検索{% else %}Search{% endif %}
                        </span>
                        <div class="d-flex align-items-center">
                            <form id="filter-form-search" method="get" class="w-100">
                                <div class="d-flex mb-0 position-relative align-items-center" style="height: 26px;">
                                    <span class="svg-icon svg-icon-3 search-icon-view" onclick="toggleSearch()">
                                        <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M13 13L10.1 10.1M11.6667 6.33333C11.6667 9.27885 9.27885 11.6667 6.33333 11.6667C3.38781 11.6667 1 9.27885 1 6.33333C1 3.38781 3.38781 1 6.33333 1C9.27885 1 11.6667 3.38781 11.6667 6.33333Z" stroke="#8E8C95" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                    </span>
                                    <input
                                        id="base-search-input" type="text" name="q" class="form-control bg-white ps-12 tw-rounded-lg"
                                        value="{% if search_q %}{{ search_q }}{% else %}""{% endif %}"
                                        placeholder="{% if LANGUAGE_CODE == 'ja' %}検索{% else %}Search{% endif %}"
                                        onkeypress="if (event.keyCode == 13)document.forms['filter-form-search'].submit();"
                                    >
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="{% include "data/utility/view-menu-search.html" %}">
                    <button 
                        onclick="openSearch()"
                        class="tw-w-[28px] tw-h-[26px] tw-justify-center tw-items-center tw-border-0 tw-bg-transparent tw-mb-2 tw-cursor-pointer hover-tooltip" type="button"
                        >
                        <span class="search-wrapper-tooltip hover-tooltip-text">
                            {% if LANGUAGE_CODE == 'ja' %}検索{% else %}Search{% endif %}
                        </span>
                        <span class="tw-flex svg-icon svg-icon-3">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <path d="M21.7 18.9L18.6 15.8C17.9 16.9 16.9 17.9 15.8 18.6L18.9 21.7C19.3 22.1 19.9 22.1 20.3 21.7L21.7 20.3C22.1 19.9 22.1 19.3 21.7 18.9Z" fill="black" />
                                <path opacity="0.3" d="M11 20C6 20 2 16 2 11C2 6 6 2 11 2C16 2 20 6 20 11C20 16 16 20 11 20ZM11 4C7.1 4 4 7.1 4 11C4 14.9 7.1 18 11 18C14.9 18 18 14.9 18 11C18 7.1 14.9 4 11 4ZM8 11C8 9.3 9.3 8 11 8C11.6 8 12 7.6 12 7C12 6.4 11.6 6 11 6C8.2 6 6 8.2 6 11C6 11.6 6.4 12 7 12C7.6 12 8 11.6 8 11Z" fill="black" />
                            </svg>
                        </span>
                    </button>
                </div>
            </div>
        </div>

        <div id="panel-bulk-action-container" class="d-none">
            <span class="me-10">
                <button class="py-1 rounded-1 btn btn-sm btn-light fw-bold mt-2 mb-1" onclick="selectAllPanel()">
                    <span>
                        {% if LANGUAGE_CODE == 'ja'%}
                        すべて選択
                        {% else %}
                        Select All
                        {% endif %}
                    </span>
                </button>
                <button class="py-1 rounded-1 btn btn-sm btn-light fw-bold mt-2 mb-1" onclick="deselectAllPanel()">
                    <span>
                        {% if LANGUAGE_CODE == 'ja'%}
                        選択を解除
                        {% else %}
                        Deselect All
                        {% endif %}
                    </span>
                </button>

                <script>
                    function selectAllPanel() {
                        selectPanelInputs = document.getElementsByClassName('panel-selection')
                        console.log(selectPanelInputs)
                        for (var i = 0; i < selectPanelInputs.length; i++) {
                            selectPanelInputs[i].checked = true;
                        }
                    }
                    function deselectAllPanel() {
                        selectPanelInputs = document.getElementsByClassName('panel-selection')
                        console.log(selectPanelInputs)
                        for (var i = 0; i < selectPanelInputs.length; i++) {
                            selectPanelInputs[i].checked = false;
                        }
                        document.getElementById('panel-bulk-action-container').classList.add('d-none')
                        document.getElementById('panel-view-container').classList.remove('d-none')
                    }
                </script>
            </span>
            <button class="py-1 rounded-1 btn btn-sm btn-light-warning fw-bold mt-2 mb-1"  type="submit"
                name="bulk_duplicate_panel"
                form="dataset-form">
                {% if LANGUAGE_CODE == 'ja'%}
                複製
                {% else %}
                Duplicate
                {% endif %}
            </button>
            <button class="py-1 rounded-1 btn btn-sm btn-light-success fw-bold mt-2 mb-1 d-none"
                type="submit"
                name="bulk_activate_panel"
                form="dataset-form">
                {% if LANGUAGE_CODE == 'ja'%}
                有効化
                {% else %}
                Activate
                {% endif %}
            </button>
            <button class="btn btn-sm btn-light-danger py-1 rounded-1 fw-bold mt-2 mb-1"  
                type="submit" 
                name="bulk_delete_panel"
                form="dataset-form">
                {% if LANGUAGE_CODE == 'ja'%}
                削除
                {% else %}
                Delete 
                {% endif %}
            </button>
            <script>
                document.body.addEventListener("hideTaskBulkActionBtn", function(evt){
                    document.getElementById('panel-bulk-action-container').classList.add('d-none')
                    document.getElementById('panel-view-container').classList.remove('d-none')
                })
            </script>
        </div>

    </div>
</div>

<div class="{% if search_q %}max-md:tw-flex {% else %}max-md:tw-hidden {% endif %}tw-hidden tw-items-center tw-py-2 border-bottom border-bottom-1 tw-transition-all tw-duration-300" id="search-bar">
    <form id="filter-form-search" method="get" class="w-100">
        <div class="d-flex mb-0 position-relative align-items-center" style="height: 26px;">
            <span class="svg-icon svg-icon-3 search-icon-view">
                <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M13 13L10.1 10.1M11.6667 6.33333C11.6667 9.27885 9.27885 11.6667 6.33333 11.6667C3.38781 11.6667 1 9.27885 1 6.33333C1 3.38781 3.38781 1 6.33333 1C9.27885 1 11.6667 3.38781 11.6667 6.33333Z" stroke="#8E8C95" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </span>
            <input id="base-search-input" type="text" name="q" class="form-control bg-white ps-12"
            value="{% if search_q %}{{ search_q }}{% else %}""{% endif %}"
            placeholder="{% if LANGUAGE_CODE == 'ja' %}検索{% else %}Search{% endif %}"
            onkeypress="if (event.keyCode == 13)document.forms['filter-form-search'].submit();"
            >
        </div>
    </form>
</div>

{% include 'data/javascript/toggleSearch.html' %}