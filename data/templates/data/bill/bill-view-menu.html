{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}


<style>
    table {
        width: auto;
    }
    
    td, th {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .dataTables_scrollHead {
        position: sticky !important;
        z-index: 3;
        background-color: white;
    }
    
</style>

{% include 'data/static/tootip-search-wrapper.html' %}

{% include "data/common/advance_search/advance-search-style.html" %}


<div class="tw-flex justify-content-between align-items-end border-bottom border-bottom-1 {% include "data/utility/tab-pane.html" %}" id="view-container-1" role="tabpanel" style="z-index:4 !important;">
    {% comment %} Desktop {% endcomment %}
    <div class="max-md:tw-hidden w-50">
        <div class="{% include "data/utility/table-nav-2.html" %}">
            
            <div class="d-flex align-items-end justify-content-between w-100" id="view-container" >
                <div class="max-md:tw-hidden w-75 d-flex align-items-center">
                    <div class="nav-item fs-6 text-gray-900">
                        <div class="d-flex align-items-center justify-content-center">
                            <button id="payment_wizard_button" type="button" class="{% include "data/utility/view-plus-link.html" %}"
                                style="height: 26px;"
                                hx-vals='{"object_type": "{{object_type}}", "view_button":"create"}'
                                hx-get="{% url 'commerce_view_setting' %}" 
                                hx-target="#expenses_form"
                                hx-on="htmx:beforeSend: 
                                document.getElementById('expenses_form_lg').innerHTML = '';"

                            >
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-plus" viewBox="0 0 16 16">
                                    <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                    
                    {% include "data/projects/partial-dropdown-view-menu.html" %}

                    <div class="{% include "data/utility/view-menu-nav-item.html" %}">

                        <a class="text-gray-600 text-hover-primary bg-transparent ms-0 me-1 pb-2 rounded-0 hover-boder-0 {% if not view_filter.view.title or view_filter.view.title == 'main' %}{% else %} border-0{% endif %}
" 
                            type="button"
                            href = "{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}">
                            {% comment %} <span class="svg-icon svg-icon-muted svg-icon-2"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M22 12C22 17.5 17.5 22 12 22C6.5 22 2 17.5 2 12C2 6.5 6.5 2 12 2C17.5 2 22 6.5 22 12ZM12 6C8.7 6 6 8.7 6 12C6 15.3 8.7 18 12 18C15.3 18 18 15.3 18 12C18 8.7 15.3 6 12 6Z" fill="currentColor"/>
                                </svg>
                            </span> {% endcomment %}
                            {% if LANGUAGE_CODE == 'ja'%}ビュー{% else %}View{% endif %}
                        </a>
                        {% if not view_filter.view.title or view_filter.view.title == 'main' %}
                        <div class="w-20px nav-item justify-content-center d-flex fs-6 text-gray-900">
                            <button id="payment_wizard_button" type="button" class="{% include "data/utility/view-plus-link.html" %}"

                                hx-vals='{"module": "{{menu_key}}", "object_type": "{{object_type}}", "view_id":"{{view.id}}"}'
                                hx-get="{% url 'commerce_view_setting' %}" 
                                hx-target="#expenses_form"
                                hx-on="htmx:beforeSend: 
                                    document.getElementById('expenses_form_lg').innerHTML = '';
                                    "
                                hx-indicator=".loading-drawer-spinner,.expenses-form"

                                >
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-three-dots-vertical" viewBox="0 0 16 16">
                                    <path d="M9.5 13a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z"/>
                                </svg>
                            </button>
                        </div>
                        {% endif %}
                    </div>
            
                    {% include 'data/projects/partial-view-menu.html' with target=object_type %}
            
                </div>
            </div>


        </div>  
    </div>  

  

    {% comment %} Mobile View {% endcomment %}
    <div class="tw-hidden max-md:tw-flex tw-w-1/2">
        <div class="d-flex align-items-center">
            <!-- Example split danger button -->
            <div class="btn-group mb-2">
                <button id="payment_wizard_button" type="button" class="tw-max-w-[90px] align-items-center d-flex btn bg-white border btn-md tw-px-[10px]"
                    style="height: 26px; border-radius: 0.475rem 0 0 0.475rem;"
                    hx-vals='{"object_type": "{{object_type}}", "view_id":"{{view.id}}"}'
                    hx-get="{% url 'commerce_view_setting' %}" 
                    hx-target="#expenses_form"
                    hx-on="htmx:beforeSend: 
                        document.getElementById('expenses_form_lg').innerHTML = '';
                        "
                >
                    {% if view.title == "main" %}
                        <span class="fs-7 ps-1 fw-bolder tw-text-ellipsis tw-overflow-hidden tw-text-nowrap">
                        {% if LANGUAGE_CODE == 'ja'%}ビュー{% else %}View{% endif %}
                        </span>
                    {% else %}
                        <span class="fs-7 ps-1 fw-bolder tw-text-ellipsis tw-overflow-hidden tw-text-nowrap">   
                            {% if view.title %}
                                {{ view.title }}
                            {% else %}
                                {% if LANGUAGE_CODE == 'ja'%}ビュー{% else %}View{% endif %}
                            {% endif %}
                        </span>
                    {% endif %}
                </button>
                <button type="button" 
                    class="tw-w-[30px] align-items-center d-flex btn btn-white border btn-md  dropdown-toggle py-1 tw-pl-2 dropdown-toggle-split" 
                    data-bs-toggle="dropdown" 
                    aria-expanded="false"
                    style="height: 26px; border-radius: 0 0.475rem 0.475rem 0;"
                >
                    <span class="svg-icon svg-icon-4">
                        <svg width=24 height=24 xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" fill="currentColor">
                            <path d="M137.4 374.6c12.5 12.5 32.8 12.5 45.3 0l128-128c9.2-9.2 11.9-22.9 6.9-34.9s-16.6-19.8-29.6-19.8L32 192c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9l128 128z"/>
                        </svg>
                    </span>
                </button>
                <ul class="dropdown-menu tw-max-w-[180px] tw-max-h-[300px] tw-overflow-y-scroll">
                    <li><a class="dropdown-item tw-text-ellipsis tw-overflow-hidden" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}">{% if LANGUAGE_CODE == 'ja' %}デフォルト{% else %}Default View{% endif %}</a></li>
                    <div class="dropdown-divider"></div>
                    {% for view in views %}
                        {% if view.title != "main" %}
                            <li><a class="dropdown-item tw-text-ellipsis tw-overflow-hidden" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?view_id={{view.id}}">{{view.title}}</a></li>
                        {% endif %}
                    {% endfor %}
                </ul>
            </div>
            <div class="fs-6 text-gray-900 mb-2">
                <button id="payment_wizard_button" type="button" class="tw-border-0 tw-bg-transparent tw-text-gray-900" 
                    hx-vals='{"object_type": "{{object_type}}", "view_button":"create"}'
                    hx-get="{% url 'commerce_view_setting' %}" 
                    hx-target="#expenses_form"
                    hx-on="htmx:beforeSend: 
                        document.getElementById('expenses_form_lg').innerHTML = '';
                        "
                    hx-trigger="click"
                    hx-swap="innerHTML">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-plus" viewBox="0 0 16 16">
                        <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                    </svg>
                </button>
            </div>
        </div>
    </div>
    
    <div class="d-flex align-items-center max-md:tw-mb-0 w-50 justify-content-end mb-3">
        <div class="max-md:tw-hidden tw-flex">
            <div class="me-2 search-wrapper expanded">
                <div class="d-flex align-items-center">
                    <form id="filter-form-search" method="get" class="w-100">
                        <div class="d-flex mb-0 position-relative align-items-center" style="height: 22px;">
                            <span class="svg-icon svg-icon-3 search-icon-view">
                                <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M13 13L10.1 10.1M11.6667 6.33333C11.6667 9.27885 9.27885 11.6667 6.33333 11.6667C3.38781 11.6667 1 9.27885 1 6.33333C1 3.38781 3.38781 1 6.33333 1C9.27885 1 11.6667 3.38781 11.6667 6.33333Z" stroke="#8E8C95" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </span>
                            <input
                            id="base-search-input" type="text" name="q" class="form-control bg-white ps-12 pe-16 h-25px me-2 tw-rounded-lg" 
                            value="{% if advance_search_filter %}{% for k, v in advance_search_filter.items %}{% if 'customer' in k and k|split:'|'|length > 1 %}{% with column_display=k|split:'|'|search_custom_field_object_order_customer:request %}{{column_display.name}}{% endwith %}{% else %}{% with args=k|add:'|'|add:object_type %}{% with column_display=args|get_column_display:request %}{{column_display.name}}{% endwith %}{% endwith %}{% endif %} {% translate_lang v.key|display_filter_rule:LANGUAGE_CODE LANGUAGE_CODE %} {{ v.value }}{% if not forloop.last %}, {% endif %}{% endfor %}{% elif search_q %}{{ search_q }}{% else %}{% endif %}"
                            placeholder={% if LANGUAGE_CODE == 'ja' %} "検索" {% else %} "Search" {% endif %}
                            onkeypress="if (event.keyCode == 13)document.forms['filter-form-search'].submit();"
                            >
                            {% if request.GET.status == 'archived' %}
                            <input type="hidden" value="archived" name="status">
                            {% endif %}
                            <span class="svg-icon svg-icon-muted svg-icon-3 filter-icon filter-drawer-btn"
                                hx-get="{% url 'advance_search_drawer' %}"
                                hx-vals='{"object_type": "{{object_type}}", "module": "{{menu_key}}", "search_scope":"True", "view_id": "{{view_id}}"}'
                                hx-indicator=".loading-drawer-spinner"
                                hx-target="#filter-drawer-content"
                                hx-trigger="click"
                                hx-swap="innerHTML"
                            >
                                <svg width="14" height="12" viewBox="0 0 14 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M1 3.33301L9 3.33301M9 3.33301C9 4.43758 9.89543 5.33301 11 5.33301C12.1046 5.33301 13 4.43758 13 3.33301C13 2.22844 12.1046 1.33301 11 1.33301C9.89543 1.33301 9 2.22844 9 3.33301ZM5 8.66634L13 8.66634M5 8.66634C5 9.77091 4.10457 10.6663 3 10.6663C1.89543 10.6663 1 9.77091 1 8.66634C1 7.56177 1.89543 6.66634 3 6.66634C4.10457 6.66634 5 7.56177 5 8.66634Z" stroke="#8E8C95" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </span>
                            <span class="svg-icon me-1 {% if advance_search.is_active %}svg-icon-primary{%else%}svg-icon-muted{% endif %} svg-icon-3 filter-icon filter-drawer-btn me-7"
                                hx-get="{% url 'advance_search_drawer' %}"
                                hx-vals='{"object_type": "{{object_type}}", "module": "{{menu_key}}", "view_id": "{{view_id}}"}'
                                hx-indicator=".loading-drawer-spinner"
                                hx-target="#filter-drawer-content"
                                hx-trigger="click"
                                hx-swap="innerHTML"
                            >
                                <svg width="14" height="10" viewBox="0 0 14 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M3 5H11M1 1H13M5 9H9" stroke="#8E8C95" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </span>
                            <input type="hidden" value="{{view_id}}" name="view_id">
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="{% include "data/utility/view-menu-search.html" %}">
            <button 
                onclick="openSearch()"
                class="tw-w-[28px] tw-h-[26px] tw-justify-center tw-items-center tw-border-0 tw-bg-transparent tw-mb-2 tw-cursor-pointer hover-tooltip" type="button"
                >
                <span class="search-wrapper-tooltip hover-tooltip-text">
                    {% if LANGUAGE_CODE == 'ja' %}検索{% else %}Search{% endif %}
                </span>
                <span class="tw-flex svg-icon svg-icon-3">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M21.7 18.9L18.6 15.8C17.9 16.9 16.9 17.9 15.8 18.6L18.9 21.7C19.3 22.1 19.9 22.1 20.3 21.7L21.7 20.3C22.1 19.9 22.1 19.3 21.7 18.9Z" fill="black" />
                        <path opacity="0.3" d="M11 20C6 20 2 16 2 11C2 6 6 2 11 2C16 2 20 6 20 11C20 16 16 20 11 20ZM11 4C7.1 4 4 7.1 4 11C4 14.9 7.1 18 11 18C14.9 18 18 14.9 18 11C18 7.1 14.9 4 11 4ZM8 11C8 9.3 9.3 8 11 8C11.6 8 12 7.6 12 7C12 6.4 11.6 6 11 6C8.2 6 6 8.2 6 11C6 11.6 6.4 12 7 12C7.6 12 8 11.6 8 11Z" fill="black" />
                    </svg>
                </span>
            </button>
        </div>

        <div class="d-flex">
            {% if object_type == constant.TYPE_OBJECT_EXPENSE %}
            <div class="{% include "data/utility/table-button.html" %}">
                <button id='view-sync-items-action-drawer' type="button" class="{% include "data/utility/gray-header-button.html" %} create-view-settings-button"
                    hx-get="{% url 'expense_action' %}" 
                    hx-trigger="click"
                    onclick="fillActionExpenseIds(this)"
                    hx-target="#manage-contacts-view-settings-drawer"
                    hx-indicator=".loading-drawer-spinner,.view-form"
                    >
                    <span class="svg-icon svg-icon-4">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-magic" viewBox="0 0 16 16">
                            <path d="M9.5 2.672a.5.5 0 1 0 1 0V.843a.5.5 0 0 0-1 0zm4.5.035A.5.5 0 0 0 13.293 2L12 3.293a.5.5 0 1 0 .707.707zM7.293 4A.5.5 0 1 0 8 3.293L6.707 2A.5.5 0 0 0 6 2.707zm-.621 2.5a.5.5 0 1 0 0-1H4.843a.5.5 0 1 0 0 1zm8.485 0a.5.5 0 1 0 0-1h-1.829a.5.5 0 0 0 0 1zM13.293 10A.5.5 0 1 0 14 9.293L12.707 8a.5.5 0 1 0-.707.707zM9.5 11.157a.5.5 0 0 0 1 0V9.328a.5.5 0 0 0-1 0zm1.854-5.097a.5.5 0 0 0 0-.706l-.708-.708a.5.5 0 0 0-.707 0L8.646 5.94a.5.5 0 0 0 0 .707l.708.708a.5.5 0 0 0 .707 0l1.293-1.293Zm-3 3a.5.5 0 0 0 0-.706l-.708-.708a.5.5 0 0 0-.707 0L.646 13.94a.5.5 0 0 0 0 .707l.708.708a.5.5 0 0 0 .707 0z"/>
                        </svg>
                    </span>
                    <span class="">
                        {% if LANGUAGE_CODE == 'ja'%}
                        アクション
                        {% else %}
                        Action
                        {% endif %}
                    </span>
                </button>
            </div>

            <script>
                {% if open_drawer == 'action_drawer_history' %}
                    $(document).ready(function() {
                        setTimeout(function() {
                            document.getElementById('view-sync-items-action-drawer').click()
                        }, 1000)
                    })
                {% endif %}
                function fillActionExpenseIds(elm) {
                    // Call your JavaScript function to generate the account IDs dynamically
                    var checkboxes = document.querySelectorAll('input[type="checkbox"]');
                    var checkedIds = [];
                    checkboxes.forEach(function(checkbox) {
                        if (checkbox.checked) {
                            checkedIds.push(checkbox.value);
                        }
                    });
                    var orderIds = checkedIds;
                    orderIds = orderIds.filter(id => id !== 'on');
                    // Now set the hx-vals attribute with the updated account IDs
                    elm.setAttribute('hx-vals', 'js:{"section": "action_history", "page": "{{page}}","action_tab":"action", "expense_ids":getSelectedExpense(), "open_drawer": "{{open_drawer}}"}');
                }
            </script>
            {% endif %}

            <div class="{% include "data/utility/table-button.html" %}">
                <button class="payment_wizard_button max-md:tw-block tw-hidden tw-w-[28px] tw-h-[26px] tw-justify-center tw-items-center tw-border-0 tw-bg-transparent tw-mb-2 tw-cursor-pointer hover-tooltip"
                type="button"
                hx-vals='{"object_type": "{{object_type}}", "view_id":"{{current_view.id}}", "download_view":true}'
                hx-get="{% url 'commerce_view_setting' %}" 
                hx-target="#expenses_form"
                hx-on="htmx:beforeSend: 
                    document.getElementById('expenses_form_lg').innerHTML = '';
                    ">
                    <span class="search-wrapper-tooltip hover-tooltip-text">
                        {% if LANGUAGE_CODE == 'ja' %}ダウンロード{% else %}Download{% endif %}
                    </span>
                    <span class="tw-flex svg-icon svg-icon-3">
                        <svg xmlns="http://www.w3.-listorg/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M21 22H3C2.4 22 2 21.6 2 21C2 20.4 2.4 20 3 20H21C21.6 20 22 20.4 22 21C22 21.6 21.6 22 21 22ZM13 13.4V3C13 2.4 12.6 2 12 2C11.4 2 11 2.4 11 3V13.4H13Z" fill="black"/>
                            <path opacity="0.3" d="M7 13.4H17L12.7 17.7C12.3 18.1 11.7 18.1 11.3 17.7L7 13.4Z" fill="black"/>
                        </svg>
                    </span>
                </button>
                
            </div>

        </div>

    </div>
</div>


{% if permission|check_permission:'edit' %}
    <div id='modal-load'
        hx-vals='{"object_type": "{{object_type}}","module": "{{menu_key}}","view_id": "{{view_id}}"}'
        hx-get="{% url 'get_bulk_update_properties' %}" 
        hx-trigger='load'
        hx-target="this">
    </div>
{% endif %}
<div id="view-header-container" class="tw-hidden w-100 pt-5 border-bottom border-bottom-1 {% include "data/utility/sub-tab-pane.html" %}" style="z-index:4 !important;" >
    <div class="d-flex align-items-center justify-content-between">
        <div class="w-100">
            {% include 'data/common/select-all-in-view-button.html' %} 
                
                {% if permission|check_permission:'edit' %}
                    <button class="py-1 rounded-1 btn btn-sm btn-light-primary fw-bold mt-2 mb-1" onclick="bulk_check_toggle(),check_permission_action(event, 'edit', 'edit_bulk_modal')" >
                        <span>
                            {% if LANGUAGE_CODE == 'ja'%}
                            編集
                            {% else %}
                            Edit
                            {% endif %}
                        </span>
                    </button>
                    
                    <button class="py-1 rounded-1 btn btn-sm btn-light-warning fw-bold mt-2 mb-1" onclick="check_permission_action(event, 'edit')" name="bulk_type" value='duplicate' type="submit" form="bulk-update">
                        {% if LANGUAGE_CODE == 'ja'%}
                        複製
                        {% else %}
                        Duplicate
                        {% endif %}
                    </button>
                {% endif %}
        
                <button class="btn btn-sm btn-light-success py-1 rounded-1 fw-bold mt-2 mb-1 d-none" type="submit" name="bulk_type" value="download" form="bulk-update">
                    {% if LANGUAGE_CODE == 'ja'%}
                    ダウンロード
                    {% else %}
                    Download 
                    {% endif %}
        
                </button>
                
                {% if permission|check_permission:'archive' %}
                <button class="py-1 rounded-1 btn btn-sm btn-light-success fw-bold mt-2 mb-1" onclick="check_permission_action(event, 'archive')" name="bulk_type" value="active" form="bulk-update">
                    {% if LANGUAGE_CODE == 'ja'%}
                    有効化
                    {% else %}
                    Activate
                    {% endif %}
                </button>
                <button class="btn btn-sm btn-light-danger py-1 rounded-1 fw-bold mt-2 mb-1" onclick="check_permission_action(event, 'archive')" name="bulk_type"  value="archive" form="bulk-update">
                    {% if LANGUAGE_CODE == 'ja'%}
                    アーカイブ
                    {% else %}
                    Archive 
                    {% endif %}
                </button>
                {% endif %}
        
                <button class="btn btn-sm btn-light-success py-1 rounded-1 fw-bold mt-2 mb-1 d-none" onclick="setBulk('download')" value="download" form="bulk-update">
                    {% if LANGUAGE_CODE == 'ja'%}
                    ダウンロード
                    {% else %}
                    Download 
                    {% endif %}
                </button>
        
                {% if permission|check_permission:'archive' %}
        
                <script>
                    function getSelectedExpense() {
                        var selectedOrders = [];
                        var classNameElements = document.getElementsByClassName("check_input");
                        if (classNameElements){
                            classNameElements.forEach(function(classNameElement) {
                                if (classNameElement.checked) {
                                    selectedOrders.push(classNameElement.value);
                                }
                            });  
                        }
                        return selectedOrders;
                    }
                </script>
        
                {% endif %}
        </div>

        <div class="d-flex">
            {% if object_type == constant.TYPE_OBJECT_EXPENSE %}
                {% if permission|check_permission:'edit' %}
                <button id='view-sync-items' type="button" class="{% include "data/utility/gray-header-button.html" %} create-view-settings-button"
                    hx-get="{% url 'expense_action' %}" 
                    hx-trigger="click"
                    onclick="fillActionExpenseIds(this),check_permission_action(event, 'edit')"
                    hx-target="#manage-contacts-view-settings-drawer"
                    hx-indicator=".loading-drawer-spinner,.view-form"
                    >
                    <span class="svg-icon svg-icon-4">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-magic" viewBox="0 0 16 16">
                            <path d="M9.5 2.672a.5.5 0 1 0 1 0V.843a.5.5 0 0 0-1 0zm4.5.035A.5.5 0 0 0 13.293 2L12 3.293a.5.5 0 1 0 .707.707zM7.293 4A.5.5 0 1 0 8 3.293L6.707 2A.5.5 0 0 0 6 2.707zm-.621 2.5a.5.5 0 1 0 0-1H4.843a.5.5 0 1 0 0 1zm8.485 0a.5.5 0 1 0 0-1h-1.829a.5.5 0 0 0 0 1zM13.293 10A.5.5 0 1 0 14 9.293L12.707 8a.5.5 0 1 0-.707.707zM9.5 11.157a.5.5 0 0 0 1 0V9.328a.5.5 0 0 0-1 0zm1.854-5.097a.5.5 0 0 0 0-.706l-.708-.708a.5.5 0 0 0-.707 0L8.646 5.94a.5.5 0 0 0 0 .707l.708.708a.5.5 0 0 0 .707 0l1.293-1.293Zm-3 3a.5.5 0 0 0 0-.706l-.708-.708a.5.5 0 0 0-.707 0L.646 13.94a.5.5 0 0 0 0 .707l.708.708a.5.5 0 0 0 .707 0z"/>
                        </svg>
                    </span>
                    <span class="">
                        {% if LANGUAGE_CODE == 'ja'%}
                        アクション
                        {% else %}
                        Action
                        {% endif %}
                    </span>
                </button>
                {% endif %}
            {% endif %}
            <div class="{% include "data/utility/table-button.html" %}">
                <button id="payment_wizard_button" type="button" class="{% include "data/utility/gray-header-button.html" %}"
                onclick="fillExportIds(this)"
                hx-get="{% url 'commerce_view_setting' %}" 
                hx-target="#expenses_form"
                        hx-on="htmx:beforeSend: 
                            document.getElementById('expenses_form_lg').innerHTML = '';
                            "
                >

                <span class="svg-icon svg-icon-4">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-upload" viewBox="0 0 16 16">
                        <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5"/>
                        <path d="M7.646 1.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 2.707V11.5a.5.5 0 0 1-1 0V2.707L5.354 4.854a.5.5 0 1 1-.708-.708z"/>
                    </svg>
                </span>
                <span class="">
                    {% if LANGUAGE_CODE == 'ja'%}
                    エクスポート
                    {% else %}
                    Export
                    {% endif %}
                </span>

            </button>

            </div>

            <script>
                function fillExportIds(elm) {
                    // Call your JavaScript function to generate the account IDs dynamically
                    var checkboxes = document.querySelectorAll('input[type="checkbox"]');
                    var flag_all = document.querySelector('#flag_all');

                    if (flag_all && flag_all.checked) {
                        var flag_all_value = 'true';
                    }
                    else {
                        var flag_all_value = 'false';
                    }
                    var checkedIds = [];
                    checkboxes.forEach(function(checkbox) {
                        if (checkbox.checked) {
                            checkedIds.push(checkbox.value);
                        }
                    });
                    var orderIds = checkedIds;
                    orderIds = orderIds.filter(id => id !== 'on');

                    console.log(orderIds)
                    // Now set the hx-vals attribute with the updated account IDs
                    elm.setAttribute('hx-vals', '{"object_type": "{{object_type}}", "view_id":"{{current_view.id}}", "download_view":true, "flag_all":"' + flag_all_value + '", "import_export_type":"export", "record_ids":"' + orderIds + '"}');
                }
            </script>
            <script>
                document.body.addEventListener("hideTaskBulkActionBtn", function(evt){
                    document.getElementById('task-bulk-action-container').classList.add('d-none')
                    document.getElementById('task-view-contianer').classList.remove('d-none')
                })
            </script>
        </div>
    </div>
    {% include 'data/common/select-all-in-view-record-msg.html' %} 
</div>

<div class="{% if search_q %}max-md:tw-flex {% else %}max-md:tw-hidden {% endif %}tw-hidden tw-items-center tw-py-2 border-bottom border-bottom-1 tw-transition-all tw-duration-300" id="search-bar">
    <form id="filter-form-search" method="get" class="w-100">
        <div class="d-flex mb-0 position-relative align-items-center" style="height: 26px;">
            <span class="svg-icon svg-icon-3 search-icon-view">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M21.7 18.9L18.6 15.8C17.9 16.9 16.9 17.9 15.8 18.6L18.9 21.7C19.3 22.1 19.9 22.1 20.3 21.7L21.7 20.3C22.1 19.9 22.1 19.3 21.7 18.9Z" fill="black" />
                    <path opacity="0.3" d="M11 20C6 20 2 16 2 11C2 6 6 2 11 2C16 2 20 6 20 11C20 16 16 20 11 20ZM11 4C7.1 4 4 7.1 4 11C4 14.9 7.1 18 11 18C14.9 18 18 14.9 18 11C18 7.1 14.9 4 11 4ZM8 11C8 9.3 9.3 8 11 8C11.6 8 12 7.6 12 7C12 6.4 11.6 6 11 6C8.2 6 6 8.2 6 11C6 11.6 6.4 12 7 12C7.6 12 8 11.6 8 11Z" fill="black" />
                </svg>
            </span>
            <input id="base-search-input" type="text" name="q" class="form-control bg-white ps-12 pe-16 h-25px me-2"
            value="{% if advance_search_filter %}{% for k, v in advance_search_filter.items %}{% if 'customer' in k and k|split:'|'|length > 1 %}{% with column_display=k|split:'|'|search_custom_field_object_order_customer:request %}{{column_display.name}}{% endwith %}{% else %}{% with args=k|add:'|'|add:object_type %}{% with column_display=args|get_column_display:request %}{{column_display.name}}{% endwith %}{% endwith %}{% endif %} {% translate_lang v.key|display_filter_rule:LANGUAGE_CODE LANGUAGE_CODE %} {{ v.value }}{% if not forloop.last %}, {% endif %}{% endfor %}{% elif search_q %}{{ search_q }}{% else %}{% endif %}"
            placeholder={% if LANGUAGE_CODE == 'ja' %} "検索" {% else %} "Search" {% endif %}
            onkeypress="if (event.keyCode == 13)document.forms['filter-form-search'].submit();"
            >
            {% if request.GET.status == 'archived' %}
            <input type="hidden" value="archived" name="status">
            {% endif %}
            <span class="svg-icon svg-icon-muted svg-icon-3 filter-icon filter-drawer-btn me-7"
                hx-get="{% url 'advance_search_drawer' %}"
                hx-vals='{"object_type": "{{object_type}}", "module": "{{menu_key}}", "search_scope":"True", "view_id": "{{view_id}}"}'
                hx-indicator=".loading-drawer-spinner"
                hx-target="#filter-drawer-content"
                hx-trigger="click"
                hx-swap="innerHTML"
            >
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path fill="black" d="M19.43 12.98c.04-.32.07-.65.07-.98s-.03-.66-.07-.98l2.11-1.65a.5.5 0 0 0 .12-.63l-2-3.46a.5.5 0 0 0-.6-.22l-2.49 1a7.03 7.03 0 0 0-1.7-.98l-.38-2.65A.5.5 0 0 0 14 2h-4a.5.5 0 0 0-.5.45l-.38 2.65c-.63.25-1.21.57-1.74.98l-2.49-1a.5.5 0 0 0-.6.22l-2 3.46a.5.5 0 0 0 .12.63L4.57 11c-.04.32-.07.65-.07.98s.03.66.07.98l-2.11 1.65a.5.5 0 0 0-.12.63l2 3.46a.5.5 0 0 0 .6.22l2.49-1c.53.41 1.11.73 1.74.98l.38 2.65c.03.24.24.45.5.45h4c.26 0 .47-.21.5-.45l.38-2.65c.63-.25 1.21-.57 1.74-.98l2.49 1a.5.5 0 0 0 .6-.22l2-3.46a.5.5 0 0 0-.12-.63l-2.11-1.65zM12 15.5A3.5 3.5 0 1 1 12 8a3.5 3.5 0 0 1 0 7.5z"/>
                </svg>
            </span>
            <span class="svg-icon {% if advance_search.is_active %}svg-icon-primary{%else%}svg-icon-muted{% endif %} svg-icon-3 filter-icon filter-drawer-btn"
                hx-get="{% url 'advance_search_drawer' %}"
                hx-vals='{"object_type": "{{object_type}}", "module": "{{menu_key}}", "view_id": "{{view_id}}"}'
                hx-indicator=".loading-drawer-spinner"
                hx-target="#filter-drawer-content"
                hx-trigger="click"
                hx-swap="innerHTML"
            >
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 7H3C2.4 7 2 6.6 2 6V4C2 3.4 2.4 3 3 3H21C21.6 3 22 3.4 22 4V6C22 6.6 21.6 7 21 7Z" fill="currentColor"/>
                    <path opacity="0.3" d="M21 14H3C2.4 14 2 13.6 2 13V11C2 10.4 2.4 10 3 10H21C21.6 10 22 10.4 22 11V13C22 13.6 21.6 14 21 14ZM22 20V18C22 17.4 21.6 17 21 17H3C2.4 17 2 17.4 2 18V20C2 20.6 2.4 21 3 21H21C21.6 21 22 20.6 22 20Z" fill="currentColor"/>
                </svg>
            </span>
            <input type="hidden" value="{{view_id}}" name="view_id">
        </div>
    </form>
</div>
 

{% include 'data/javascript/toggleSearch.html' %} 

{% comment %} <script>
    var index_1 = '';
    var index_2 = '';
    const checking_checkbox = (elem,event) => {
        if (event.shiftKey) {
            check_inputs = document.getElementsByClassName('check_input')
            
            index_2 = elem;

            //Do Check
            var pos_1 = '';
            var pos_2 = '';
            for (var i = 0; i < check_inputs.length; i++) {
                if (index_1 == check_inputs[i]){
                    pos_1 = i;
                }
                else if (index_2 == check_inputs[i]){
                    pos_2 = i;
                }
            }
            
            if (pos_1 > pos_2){
                for (var i = pos_2; i < pos_1; i++) {
                    check_inputs[i].checked = true;
                }
            }
            else {
                for (var i = pos_1; i < pos_2; i++) {
                    console.log(i);
                    check_inputs[i].checked = true;
                    
                }
            }
        }
        else{
            if (elem.checked) {
                index_1 = elem;
                document.getElementById('view-header-container').classList.remove('tw-hidden')
                document.getElementById('view-header-container').classList.add('tw-hidden')
            } else {
                taskSelections = document.querySelectorAll('input[type=checkbox]')
                for (let i = 0; i < taskSelections.length; i++) {
                    const element = taskSelections[i];
                    console.log(element.checked)
                    if (element.checked) {
                        return
                    }
                }
                document.getElementById('view-header-container').classList.add('tw-hidden')
                document.getElementById('view-header-container').classList.remove('tw-hidden')
            }
        }
    }
</script> {% endcomment %}