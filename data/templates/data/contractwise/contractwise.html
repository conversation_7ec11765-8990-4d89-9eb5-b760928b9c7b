{% extends 'base.html' %}
{% load i18n %}
{% load custom_tags %}
{% load humanize %}
{% load hosts %}
{% if source_integration %}
    {% apply_language_from_hubspot request.LANGUAGE_CODE as LANGUAGE_CODE %}
{% else %}
    {% get_current_language as LANGUAGE_CODE %}
{% endif %}
{% block content %}

{% include "data/utility/table-css.html" %}
{% include 'data/static/tootip-search-wrapper.html' %}
<div class="d-flex d-flex align-items-center">
    {% include 'data/common/module-header-tabs.html' %}

    <div class="w-50 d-flex justify-content-end tw-mr-5" id="contract-view-container">
        {% if permission|check_permission:'edit' %}

            <button class="w-100px align-items-center d-flex btn btn-primary btn-md py-1 rounded-1 manage-full-wizard-button" type="button"
                hx-get="{% host_url 'document_form' host 'app' %}" 
                hx-target="#manage-full-drawer-content"
                hx-vals = '{"view_id":"{{view_id}}", "module": "{{menu_key}}"}'
                hx-indicator=".loading-drawer-spinner" 
                style="height: 32px;"
                >
                <span class="svg-icon svg-icon-4">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <rect opacity="0.3" x="2" y="2" width="20" height="20" rx="10" fill="currentColor"/>
                        <rect x="10.8891" y="17.8033" width="12" height="2" rx="1" transform="rotate(-90 10.8891 17.8033)" fill="currentColor"/>
                        <rect x="6.01041" y="10.9247" width="12" height="2" rx="1" fill="currentColor"/>
                    </svg>
                </span>
        
                <span class="fs-7 ps-1 fw-bolder w-85px">
                    {% if LANGUAGE_CODE == 'ja'%}
                    新規
                    {% else %}
                    New
                    {% endif %}
                </span>
            </button>
        {% endif %}
    </div>
</div>

{% if permission == 'hide' %}
    {% include 'data/static/no-access-page.html' %}
{% else %}
    <div class="{% include "data/utility/table-container.html" %}">
        {% include 'data/common/permission-action-warning-message.html' %}

        {% comment %} Views Part {% endcomment %}
            <div class="{% include "data/utility/tab-pane.html" %}" role="tabpanel" style="z-index:4 !important;">
                <div class="{% include "data/utility/table-nav.html" %}">
                    <div class="{% include "data/utility/table-content.html" %}" id="contract-view-container-1">
                        <div class="w-50">
                            <div class="d-flex align-items-center">
                                <div class="nav-item me-1 d-flex align-items-center">
                                    <a class="{% include "data/utility/view-menu-default.html" %}" 
                                        type="button"
                                        href = "{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}">
                                        <span class="svg-icon svg-icon-muted svg-icon-2"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M22 12C22 17.5 17.5 22 12 22C6.5 22 2 17.5 2 12C2 6.5 6.5 2 12 2C17.5 2 22 6.5 22 12ZM12 6C8.7 6 6 8.7 6 12C6 15.3 8.7 18 12 18C15.3 18 18 15.3 18 12C18 8.7 15.3 6 12 6Z" fill="currentColor"/>
                                            </svg>
                                        </span>
                                    </a>
                                    {% if not view_filter.view.title %}
                                    <div class="w-20px nav-item justify-content-center d-flex fs-6 text-gray-900">
                                        <button type="button" class="{% include "data/utility/view-plus-link.html" %} manage-view-settings-button"
                                            hx-vals='{"module": "{{menu_key}}", "object_type": "{{object_type}}", "view_id":"{{view_filter.view.id}}"}'
                                            hx-get="{% url 'commerce_view_setting' %}" 
                                            hx-indicator=".loading-drawer-spinner,.view-form"
                                            hx-target="#manage-contacts-view-settings-drawer"
                                            >
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-three-dots-vertical" viewBox="0 0 16 16">
                                                <path d="M9.5 13a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z"/>
                                            </svg>
                                        </button>
                                    </div>
                                    {% endif %}
                                </div>

                                {% comment %} Put Items here {% endcomment %}
                                {% include 'data/projects/partial-view-menu.html' %}

                                <div class="nav-item fs-6 text-gray-900">
                                    <button type="button" class="{% include "data/utility/view-plus-link.html" %} manage-view-settings-button" 
                                        hx-vals='{"object_type": "{{object_type}}", "view_button":"create"}'
                                        hx-get="{% url 'commerce_view_setting' %}" 
                                        hx-indicator=".loading-drawer-spinner,.view-form"
                                        hx-target="#manage-contacts-view-settings-drawer"
                                        
                                        >
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-plus" viewBox="0 0 16 16">
                                            <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex w-50 justify-content-end">
                            <div class="max-md:tw-hidden tw-flex me-2">
                                <div class="{% include "data/utility/search-wrapper.html" %} hover-tooltip">
                                    <span class="search-wrapper-tooltip hover-tooltip-text">
                                        {% if LANGUAGE_CODE == 'ja' %}検索{% else %}Search{% endif %}
                                    </span>
                                    <div class="d-flex align-items-center">
                                        <form id="filter-form-search" method="get" class="w-100">
                                            <div class="d-flex mb-0 position-relative align-items-center" style="height: 26px;">
                                                <span class="svg-icon svg-icon-3 search-icon-view" onclick="toggleSearch()">
                                                    <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M13 13L10.1 10.1M11.6667 6.33333C11.6667 9.27885 9.27885 11.6667 6.33333 11.6667C3.38781 11.6667 1 9.27885 1 6.33333C1 3.38781 3.38781 1 6.33333 1C9.27885 1 11.6667 3.38781 11.6667 6.33333Z" stroke="#8E8C95" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                                    </svg>
                                                </span>
                                                <input
                                                id="base-search-input" type="text" name="q" class="form-control bg-white ps-12 tw-rounded-lg"
                                                value={% if search_q %} "{{ search_q }}" {% else %}""{% endif %}
                                                placeholder={% if LANGUAGE_CODE == 'ja' %} "契約を検索" {% else %} "Search Contract" {% endif %}
                                                onkeypress="if (event.keyCode == 13)document.forms['filter-form-search'].submit();"
                                                >
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>

                            <div class="{% include "data/utility/view-menu-search.html" %}">
                                <button 
                                    onclick="openSearch()"
                                    class="tw-w-[28px] tw-h-[26px] tw-justify-center tw-items-center tw-border-0 tw-bg-transparent tw-mb-2 tw-cursor-pointer hover-tooltip" type="button"
                                    >
                                    <span class="search-wrapper-tooltip hover-tooltip-text">
                                        {% if LANGUAGE_CODE == 'ja' %}検索{% else %}Search{% endif %}
                                    </span>
                                    <span class="tw-flex svg-icon svg-icon-3">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                            <path d="M21.7 18.9L18.6 15.8C17.9 16.9 16.9 17.9 15.8 18.6L18.9 21.7C19.3 22.1 19.9 22.1 20.3 21.7L21.7 20.3C22.1 19.9 22.1 19.3 21.7 18.9Z" fill="black" />
                                            <path opacity="0.3" d="M11 20C6 20 2 16 2 11C2 6 6 2 11 2C16 2 20 6 20 11C20 16 16 20 11 20ZM11 4C7.1 4 4 7.1 4 11C4 14.9 7.1 18 11 18C14.9 18 18 14.9 18 11C18 7.1 14.9 4 11 4ZM8 11C8 9.3 9.3 8 11 8C11.6 8 12 7.6 12 7C12 6.4 11.6 6 11 6C8.2 6 6 8.2 6 11C6 11.6 6.4 12 7 12C7.6 12 8 11.6 8 11Z" fill="black" />
                                        </svg>
                                    </span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div id="contract-bulk-action-container" class="tw-hidden d-flex w-100">
                        <div class="max-md:tw-block tw-flex w-50">
                            <div class="me-10 d-flex" style="min-width: 30px">
                                <button class="py-1 mt-2 mb-1 rounded-1 btn btn-sm btn-light fw-bold me-2" onclick="selectAllContacts()">
                                    <span>
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        すべて選択
                                        {% else %}
                                        Select All
                                        {% endif %}
                                    </span>
                                </button>
                                <button class="py-1 mt-2 mb-1 rounded-1 btn btn-sm btn-light fw-bold me-2" onclick="deselectAllContacts()">
                                    <span>
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        選択を解除
                                        {% else %}
                                        Deselect All
                                        {% endif %}
                                    </span>
                                </button>
                
                                <script>
                                    function selectAllContacts() {
                                        selectTaskInputs = document.getElementsByClassName('contract-selection')
                                        for (var i = 0; i < selectTaskInputs.length; i++) {
                                            selectTaskInputs[i].checked = true;
                                        }
                                    }
                                    function deselectAllContacts() {
                                        selectTaskInputs = document.getElementsByClassName('contract-selection')
                                        for (var i = 0; i < selectTaskInputs.length; i++) {
                                            selectTaskInputs[i].checked = false;
                                        }
                                        document.getElementById('contract-bulk-action-container').classList.add('tw-hidden')
                                        document.getElementById('contract-view-container').classList.remove('tw-hidden')
                                        document.getElementById('contract-view-container-1').classList.remove('tw-hidden')
                                    }
                                </script>
                            </div>
                            {% if permission|check_permission:'archive' %}
                            <button class="py-1 mt-2 mb-1 rounded-1 btn btn-sm btn-light-success fw-bold me-2" onclick="check_permission_action(event, 'archive', 'manage_restore_bulk')" >
                                {% if LANGUAGE_CODE == 'ja'%}
                                有効化
                                {% else %}
                                Activate
                                {% endif %}
                            </button>
                            <button class="py-1 mt-2 mb-1 btn btn-sm btn-light-danger rounded-1 fw-bold me-2" onclick="check_permission_action(event, 'archive', 'manage_delete_bulk')" >
                                {% if LANGUAGE_CODE == 'ja'%}
                                アーカイブ
                                {% else %}
                                Archive 
                                {% endif %}
                            </button>
                            {% endif %} 

                            <script>
                                document.body.addEventListener("hideTaskBulkActionBtn", function(evt){
                                    document.getElementById('contract-bulk-action-container').classList.add('tw-hidden')
                                    document.getElementById('contract-view-contianer').classList.remove('tw-hidden')
                                })
                            </script>

                            <script>
                                function check_permission_action(event, permission_type, ...args){
                                    let source = args.length > 0 ? args[0] : null;
                                    
                                    const checkInputs = document.querySelectorAll('.check_input:checked');

                                    let members = "{{group_members}}"
                                    members = members.split(',')
                                    const user_id = '{{request.user.id}}'
                                    const permission = '{{permission}}';
                                    const permission_list = permission.split('|');
                                    let scope = ''
                                    permission_list.forEach(p => {
                                        if (p.includes(permission_type)) {
                                            p_split = p.split('_');
                                            scope = p_split[0]
                                        }
                                    })
                                    let msg = '';
                                    let denied = false;
                                    for (let i = 0; i < checkInputs.length; i++) {
                                        var owner_id = checkInputs[i].dataset.owner;
                                        console.log('owner ', owner_id)
                                        if (owner_id){
                                            if (scope == 'user'){
                                                if (owner_id.toString() !== user_id.toString()) {
                                                    {% if LANGUAGE_CODE == 'ja' %}
                                                    msg = "操作が拒否されました。自分のアイテムのみ編集または削除することができます。";
                                                    {% else %}
                                                    msg = "Action denied. You are only allowed to edit or delete your own items.";
                                                    {% endif %}                    
                                                    checkInputs[i].click()
                                                    denied = true;
                                                }
                                            } else if (scope == 'team'){
                                                if (!members.includes(owner_id.toString())) {
                                                    {% if LANGUAGE_CODE == 'ja' %}
                                                    msg = "操作が拒否されました。自分または自分のチームに割り当てられたアイテムのみ編集または削除できます。";
                                                    {% else %}
                                                    msg = "Action denied. You can only edit or delete items assigned to you or your team.";
                                                    {% endif %}
                                                    checkInputs[i].click()
                                                    denied = true;
                                                }
                                            } 
                                        }
                                    }
                                    if (denied) {
                                        event.preventDefault();
                                        event.stopImmediatePropagation();
                                        document.getElementById('permissionActionWarning').innerHTML = msg;
                                        setTimeout(() => {
                                            document.getElementById('permissionActionWarning').innerHTML = '';
                                        }, 4000);
                                        msg = ''
                                    } else if (source){
                                        const modalEl = document.getElementById(source);
                                        const modal = bootstrap.Modal.getOrCreateInstance(modalEl);
                                        modal.show();
                                    }
                                    
                                }
                            </script>
                        </div>
                        <div class="d-flex w-50 align-items-center justify-content-end">
                            {% if permission|check_permission:'edit' %}
                            <div class="{% include "data/utility/table-button.html" %}">
                                <button type="button" class="{% include "data/utility/gray-header-button.html" %} create-view-settings-button"
                                    hx-get="{% url 'shopturbo_load_drawer' %}" 
                                    hx-vals='{"module": "{{menu_key}}"}'
                                    hx-trigger="click"
                                    onclick="fillItemExportIds(this)"
                                    hx-target="#manage-contacts-view-settings-drawer"

                                    >
                                    <span class="svg-icon svg-icon-4">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-upload" viewBox="0 0 16 16">
                                            <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5"/>
                                            <path d="M7.646 1.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 2.707V11.5a.5.5 0 0 1-1 0V2.707L5.354 4.854a.5.5 0 1 1-.708-.708z"/>
                                        </svg>
                                    </span>
                                    <span class="">
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        エクスポート
                                        {% else %}
                                        Export
                                        {% endif %}
                                    </span>
                                </button>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                </div>
            </div>
        {% comment %} End of Views {% endcomment %}

        <div class="{% if search_q %}max-md:tw-flex {% else %}max-md:tw-hidden {% endif %}tw-hidden tw-items-center tw-py-2 border-bottom border-bottom-1 tw-transition-all tw-duration-300" id="search-bar">
            <form id="filter-form-search" method="get" class="w-100">
                <div class="d-flex mb-0 position-relative align-items-center" style="height: 26px;">
                    <span class="svg-icon svg-icon-3 search-icon-view">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M21.7 18.9L18.6 15.8C17.9 16.9 16.9 17.9 15.8 18.6L18.9 21.7C19.3 22.1 19.9 22.1 20.3 21.7L21.7 20.3C22.1 19.9 22.1 19.3 21.7 18.9Z" fill="black" />
                            <path opacity="0.3" d="M11 20C6 20 2 16 2 11C2 6 6 2 11 2C16 2 20 6 20 11C20 16 16 20 11 20ZM11 4C7.1 4 4 7.1 4 11C4 14.9 7.1 18 11 18C14.9 18 18 14.9 18 11C18 7.1 14.9 4 11 4ZM8 11C8 9.3 9.3 8 11 8C11.6 8 12 7.6 12 7C12 6.4 11.6 6 11 6C8.2 6 6 8.2 6 11C6 11.6 6.4 12 7 12C7.6 12 8 11.6 8 11Z" fill="black" />
                        </svg>
                    </span>
                    <input id="base-search-input" type="text" name="q" class="form-control bg-white ps-12"
                    value={% if search_q %} "{{ search_q }}" {% else %}""{% endif %}
                    placeholder={% if LANGUAGE_CODE == 'ja' %} "ロケーション検索" {% else %} "Search Location" {% endif %}
                    onkeypress="if (event.keyCode == 13)document.forms['filter-form-search'].submit();"
                    >
                    <input type="hidden" value="{{view_id}}" name="view_id">
                </div>
            </form>
        </div>

        <div class="mb-2 d-none" id="d-select-additional-options">
            <div class="status bg-light-dark text-center pt-4 pb-3 px-5 rounded">
                
                {% if LANGUAGE_CODE == 'ja'%}
                このページの全てのレコードが選択されました。 
                {% else %}
                All records on this page are selected. 
                {% endif %}

                <a onclick="toggleText()" 
                    class="btn btn-dark" 
                    data-bs-toggle="collapse" 
                    id="select-additional-options-toggle" 
                    role="button" 
                    aria-expanded="false" 
                    aria-controls="collapseExample">
                    {% if LANGUAGE_CODE == 'ja'%}
                    全てのレコードを選択する
                    {% else %}
                    Select all records
                    {% endif %}

                    </a>
            </div>
        </div>
        
        <form method="POST" id="contract-form">
            {% csrf_token %}

            <input type="hidden" name="module_slug" value="{{menu_key}}">
            <input type="hidden" name="page" value="{{page}}">
            <input type="hidden" name="view_id" value="{{view_id}}">
            
            <div class="pt-0">
                {% include 'data/contractwise/contract-table.html' %}
                <input name='flag_all' class="flag_all" hidden></input>
                
                <div class="modal fade" tabindex="-1" id="manage_delete_bulk">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header pb-0 border-0 justify-content-end">
                                <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                                </div>
                            </div>
                            <div class="modal-body pb-0">
                                <div class="mb-13 text-center">
                                    <h3 class="modal-title">
                                        
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        一括アーカイブの確認
                                        {% else %}
                                        Bulk Archive Confirmation
                                        {% endif %}
                                    
                                    </h3>
                                </div>
                                <div class="border-bottom">
                                    <div class="fv-rowd-flex flex-column mb-8">
                                        <label class="d-flex justify-content-center align-items-center fs-6 fw-bold mb-2">
                                            <span class="">
                                                {% if LANGUAGE_CODE == 'ja'%}
                                                選択されたレコードをアーカイブしてもよろしいですか?
                                                {% else %}
                                                Are you sure to archive selected records?
                                                {% endif %}
                                            </span>
                                        </label>
                                
                                    </div>
                                </div>
                            </div>
                            
            
                            <div class="modal-footer border-0">
                                <button name="bulk_delete_items" type="submit" class="btn btn-danger">
                                    
                                    {% if LANGUAGE_CODE == 'ja'%}
                                        アーカイブ
                                        {% else %}
                                        Archive
                                        {% endif %}
                                    </button>
                                
                                </button>
                                <a data-bs-dismiss="modal" class="btn btn-dark">
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    キャンセル
                                    {% else %}
                                    Cancel
                                    {% endif %}
                                </a>
                            </div>
                        </div>
                    </div>
                </div> 

                <div class="modal fade" tabindex="-1" id="manage_restore_bulk">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header pb-0 border-0 justify-content-end">
                                <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                                </div>
                            </div>
                            <div class="modal-body pb-0">
                                <div class="mb-13 text-center">
                                    <h3 class="modal-title">
                                        
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        一括有効化の確認
                                        {% else %}
                                        Bulk Activate Confirmation
                                        {% endif %}
                                    
                                    </h3>
                                </div>
                                <div class="border-bottom">
                                    <div class="fv-rowd-flex flex-column mb-8">
                                        <label class="{% include 'data/utility/form-label.html' %}">
                                            <span class="">
                                                {% if LANGUAGE_CODE == 'ja'%}
                                                これらのロケーションを有効にしてもよろしいですか?
                                                {% else %}
                                                Are you sure to activate these locations?
                                                {% endif %}
                                            </span>
                                        </label>
                                
                                    </div>
                                </div>
                            </div>
                            
            
                            <div class="modal-footer border-0">
                                <button name="bulk_restore_items" type="submit" class="btn btn-success">
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    有効化
                                    {% else %}
                                    Activate
                                    {% endif %}
                                </button>
                                <a data-bs-dismiss="modal" class="btn btn-dark">
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    キャンセル
                                    {% else %}
                                    Cancel
                                    {% endif %}
                                </a>
                            </div>
                        </div>
                    </div>
                </div> 

            </div>
        </form>

        <div class="{% include "data/utility/pagination.html" %}">
            {% if LANGUAGE_CODE == 'ja'%}
            {{paginator_item_begin}}–{{paginator_item_end}} の {{paginator.count}} 件
            {% else %}
            Viewing {{paginator_item_begin}}–{{paginator_item_end}} of {{paginator.count}} results
            {% endif %}
            <div>
                {% if page_content.has_previous %}     
                    <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300 w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page=1&{% query_transform %}">{% translate_lang "First" LANGUAGE_CODE %}</a>
                    <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300 w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page={{ page_content.previous_page_number }}&{% query_transform %}">{% translate_lang "Previous" LANGUAGE_CODE %}</a>
                {% endif %}
                        
                {% if page_content.has_next %}
                    <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300 w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page={{ page_content.next_page_number }}&{% query_transform %}">{% translate_lang "Next" LANGUAGE_CODE %}</a>
                    <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300 w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page={{ page_content.paginator.num_pages }}&{% query_transform %}">{% translate_lang "Last" LANGUAGE_CODE %}</a>
                {% endif %}
            </div>
        </div>
    </div>


<script>
    function toggleText() {
        var x = document.getElementById("select-additional-options-toggle");
        var toggle_data = x.getAttribute('toggle-data')
        if (toggle_data !== "true") {
                
                {% if LANGUAGE_CODE == 'ja'%}
                x.innerHTML = "選択を解除";
                {% else %}
                x.innerHTML = "Clear All";
                {% endif %}

          $(".flag_all").each(function(index, element) {
                element.value = true
            });

            x.setAttribute('toggle-data',"true")

        } else {

            x.setAttribute('toggle-data',"false")

            
            $('input[type=checkbox]').prop('checked', false);

            //Hide
            var element_select_options = document.getElementById("d-select-additional-options");
            element_select_options.classList.add("d-none")

            x.innerHTML = "Select All {{paginator.count}} contacts in this sections";
            
            $(".flag_all").each(function(index, element) {
                element.value = false
            });

        }
      }



    function select_all() {
        $('#selectAll').prop('checked', !$('#selectAll').prop('checked'));
        if ($('#selectAll').prop('checked') == true) {
            //downloadelem = document.getElementById("csv_download");
            //downloadelem.classList.add("disabled");
            
            $('input[type=checkbox]').prop('checked', false);

            var element_select_options = document.getElementById("d-select-additional-options");
            element_select_options.classList.add("d-none")

        } else {
            //downloadelem = document.getElementById("csv_download");
            //downloadelem.classList.remove("disabled");

            $('input[type=checkbox]').prop('checked', true);

            var element_select_options = document.getElementById("d-select-additional-options");
            element_select_options.classList.remove("d-none")

        }
    }


    var contactChecked = {}
    var first_id_check_box = null;
    const checking_checkbox = (elem,event) => {
        if (elem.checked) {
            document.getElementById('contract-bulk-action-container').classList.remove('tw-hidden')
            document.getElementById('contract-view-container').classList.add('tw-hidden')
            document.getElementById('contract-view-container-1').classList.add('tw-hidden')
        } else {
            taskSelections = document.getElementsByClassName('contract-selection')
            for (let i = 0; i < taskSelections.length; i++) {
                const element = taskSelections[i];
                if (element.checked) {
                    return
                }
            }
            document.getElementById('contract-bulk-action-container').classList.add('tw-hidden')
            document.getElementById('contract-view-container').classList.remove('tw-hidden')
            document.getElementById('contract-view-container-1').classList.remove('tw-hidden')
        }
    }
    
    {% include "data/common/open-drawer.js" %}
</script>


{% if contract_id %}
    <a id="selected_show_items" class="d-none manage-full-wizard-button"
        hx-get="{% host_url 'document_form' host 'app' %}?contract_id={{contract_id}}"
        hx-vals = '{"view_id":"{{view_id}}", "module": "{{menu_key}}" , "page": "{{page}}"}'
        hx-target="#manage-full-drawer-content"
        hx-indicator=".loading-drawer-spinner"
        hx-trigger="click"
    ></a>
    <script>
        $(document).ready(function() {
            setTimeout(function() {
                document.getElementById('selected_show_items').click();
            }, 0);
        });
    </script>
{% endif %}


{% include 'data/javascript/toggleSearch.html' %} 

{% endif %}


{% comment %} F<div class="px-10 w-100">
    <style>
        #canvasContainer {
            position: relative;
            width: 800px;
            height: 1000px;
        }

        #dragme {
            position: absolute;
            width: 100px;
            height: 50px;
            cursor: move;
            z-index:2;
            resize: both;
            overflow: hidden;
            border: 2px solid #472CF5;
            min-width: 50px;
            min-height: 30px;
            max-width: 100%;
            max-height: 100%
        }

        .resizable-handle {
            width: 10px;
            height: 10px;
            background-color: white;
            position: absolute;
            bottom: 0;
            right: 0;
            cursor: nwse-resize;
        }
    </style>

    <div class="mb-10">
        <div class="py-5 mb-5 align-items-center py-0">
            <div class="mb-10">
            </div>


            <div class="d-flex justify-content-center align-items-center" draggable="true" id="dragme">
                Signature Placeholder
                <div class="resizable-handle border" onclick="" id="myResizableHandle"></div>
            </div>

            <div id="canvasContainer">
                <canvas class="border" id="pdfCanvas"></canvas>
            </div>
            <div id="dragDiv"></div>
            <script>
                const resizeObserver = new ResizeObserver((entries) => {
                    for (const entry of entries) {
                        const elm = entry.target
                        xCoord = elm.getBoundingClientRect().right - document.body.scrollLeft - elm.clientWidth
                        yCoord = elm.getBoundingClientRect().bottom - document.body.scrollTop - elm.clientHeight
                        maxX = pdfCanvas.getBoundingClientRect().right - document.body.scrollLeft - elm.clientWidth
                        maxY = pdfCanvas.getBoundingClientRect().bottom - document.body.scrollTop - elm.clientHeight

                    }
                });

                resizeObserver.observe(document.getElementById('dragme'))


                function drag_start(event) {
                    var style = window.getComputedStyle(event.target, null);
                    var str = (parseInt(style.getPropertyValue("left")) - event.clientX) + ',' + (parseInt(style.getPropertyValue("top")) - event.clientY) + ',' + event.target.id;
                    event.dataTransfer.setData("Text", str);
                }
    
                function drop(event) {
                    var offset = event.dataTransfer.getData("Text").split(',');
                    var dm = document.getElementById(offset[2]);
                    const maxX = pdfCanvas.getBoundingClientRect().right + window.scrollX - dm.clientWidth;
                    const maxY = pdfCanvas.getBoundingClientRect().bottom + window.scrollY - dm.clientHeight;
                    const minX = pdfCanvas.getBoundingClientRect().left + window.scrollX;
                    const minY = pdfCanvas.getBoundingClientRect().top + window.scrollY;

                    newLeft = (event.clientX + parseInt(offset[0], 10)) 
                    newTop = (event.clientY + parseInt(offset[1], 10))

                    console.log(maxX, maxY, newLeft-canvasContainer , newTop);

                    dm.style.left = Math.min(Math.max(minX, newLeft), maxX) + 'px';
                    dm.style.top = Math.min(Math.max(minY, newTop), maxY) + 'px';
                    event.preventDefault();
                    return false;
                }
    
                function drag_over(event) {
                    event.preventDefault();
                    return false;
                }
                var dm = document.getElementById('dragme');
                dm.addEventListener('dragstart',drag_start);
                document.body.addEventListener('dragover',drag_over);
                document.body.addEventListener('drop',drop); 



                // Loaded via <script> tag, create shortcut to access PDF.js exports.
                pdfjsLib.GlobalWorkerOptions.workerSrc = "https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js"
                const pdfUrl = "https://nyc3.digitaloceanspaces.com/sankafile/static/contract-documents/dd67fdd1-71c0-42ee-8cfd-d8c536e8d299.pdf"

                const canvasContainer = document.getElementById('canvasContainer');
                const dragDiv = document.getElementById('dragDiv');
                const pdfCanvas = document.getElementById('pdfCanvas');
                let isDragging = false;
                let offsetX, offsetY;

                dragDiv.addEventListener('mousedown', handleMouseDown);
                document.addEventListener('mousemove', handleMouseMove);
                document.addEventListener('mouseup', handleMouseUp);

                function handleMouseDown(event) {
                    isDragging = true;
                    offsetX = event.clientX - dragDiv.getBoundingClientRect().left;
                    offsetY = event.clientY - dragDiv.getBoundingClientRect().top;
                }

                function handleMouseMove(event) {
                    if (isDragging) {
                        const x = event.clientX - offsetX;
                        const y = event.clientY - offsetY;

                        dragDiv.style.left = `${x}px`;
                        dragDiv.style.top = `${y}px`;
                    }
                }

                function handleMouseUp() {
                    isDragging = false;
                }

            </script>
        </div>
    </div>

</div> {% endcomment %}


{% endblock %}