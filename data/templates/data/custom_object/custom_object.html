{% extends 'base.html' %}
{% load i18n %}
{% load custom_tags %}
{% load humanize %}
{% load hosts %}
{% block content %}


{% include "data/utility/table-css.html" %}
{% include 'data/javascript/toggleSearch.html' %}

{% include 'data/static/tootip-search-wrapper.html' %}
<div class="d-flex d-flex align-items-center align-items-center" style="background-color: #FAFAFA !important;">
    {% include 'data/common/module-header-tabs.html' %}

    <div class="w-50 d-flex justify-content-end tw-mr-5" id="custom-object-view-container-1">
        <div class="{% include "data/utility/header-action-button.html" %}">
            <button class="btn tw-font-[500] tw-rounded-l-lg tw-rounded-r-none tw-border-0 tw-bg-gray-200 hover:tw-bg-gray-300 max-md:tw-hidden w-125px align-items-center d-flex svg-icon-gray-600 py-1 justify-content-center px-1 custom-object-create-wizard-btn" type="button"
                hx-get="{% url 'custom_object_drawer' custom_object.id %}"
                hx-vals='{"drawer-type":"custom-object-view-export-import","page":  "{{custom_object.slug}}", "view_id":"{{view_filter.view.id}}", "import_export_type":"export", "module": "{{menu_key}}"}'
                hx-trigger="click"
                hx-include="[name='checkbox']"
                hx-target="#custom-object-create-wizard-drawer"
                >
                <span class="svg-icon svg-icon-4">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-upload" viewBox="0 0 16 16">
                        <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5"/>
                        <path d="M7.646 1.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 2.707V11.5a.5.5 0 0 1-1 0V2.707L5.354 4.854a.5.5 0 1 1-.708-.708z"/>
                    </svg>
                </span>
                <span class="">
                    {% if LANGUAGE_CODE == 'ja'%}
                    エクスポート
                    {% else %}
                    Export
                    {% endif %}
                </span>
            </button>

            <button id='view-sync-items' type="button" class="btn tw-font-[500] tw-rounded-l-none tw-rounded-r-lg tw-border-0 tw-bg-gray-200 hover:tw-bg-gray-300 max-md:tw-hidden w-125px align-items-center d-flex svg-icon-gray-600 py-1 justify-content-center px-1 custom-object-create-wizard-btn"
                hx-get="{% url 'custom_object_drawer' custom_object.id %}"
                hx-vals='{"drawer-type":"custom-object-view-export-import","view_id":"{{view_filter.view.id}}","page": "{{custom_object.slug}}","import_export_type":"import", "module": "{{menu_key}}"}'
                hx-include="[name='checkbox']"
                hx-target="#custom-object-create-wizard-drawer"
                hx-trigger="click"
                >
                <span class="svg-icon svg-icon-4">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-download" viewBox="0 0 16 16">
                        <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5"/>
                        <path d="M7.646 11.854a.5.5 0 0 0 .708 0l3-3a.5.5 0 0 0-.708-.708L8.5 10.293V1.5a.5.5 0 0 0-1 0v8.793L5.354 8.146a.5.5 0 1 0-.708.708z"/>
                    </svg>
                </span>
                <span class="">
                    {% if LANGUAGE_CODE == 'ja'%}
                    インポート
                    {% else %}
                    Import
                    {% endif %}
                </span>
            </button>
        </div>

        {% if permission|check_permission:'edit' %}
        <div class="btn-group tw-h-[32px]">
            <button class="tw-w-[100px] align-items-center d-flex btn btn-primary btn-md rounded-1 custom-object-create-wizard-btn py-1" type="button"
                hx-get="{% url 'custom_object_drawer' custom_object.id %}"
                hx-vals = '{"drawer-type":"create-object", "view_id": "{{view_filter.view.id}}", "module": "{{menu_key}}"}'
                hx-target="#custom-object-create-wizard-drawer"
                hx-indicator=".loading-drawer-spinner,#custom-object-create-wizard-drawer"
                >
                <span class="svg-icon svg-icon-4">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <rect opacity="0.3" x="2" y="2" width="20" height="20" rx="10" fill="currentColor"/>
                        <rect x="10.8891" y="17.8033" width="12" height="2" rx="1" transform="rotate(-90 10.8891 17.8033)" fill="currentColor"/>
                        <rect x="6.01041" y="10.9247" width="12" height="2" rx="1" fill="currentColor"/>
                    </svg>
                </span>
        
                <span class="fs-7 ps-1 fw-bolder w-85px">
                    {% if LANGUAGE_CODE == 'ja'%}
                    新規
                    {% else %}
                    New
                    {% endif %}
                </span>
            </button> 
        </div>
        {% endif %}
    </div>
</div>

{% if permission == 'hide' %}
    {% include 'data/static/no-access-page.html' %}
{% else %}
<div class="{% include "data/utility/table-container.html" %}">
    {% comment %} Views Part {% endcomment %}
    <div class="{% include "data/utility/tab-pane.html" %} pt-5" role="tabpanel" style="z-index:4 !important;">
        <div class="{% include "data/utility/table-nav.html" %}">
            <div class="{% include "data/utility/table-content.html" %}" id="custom-object-view-container">
                {% comment %} Desktop {% endcomment %}
                <div class="max-md:tw-hidden w-75 d-flex align-items-center">
                    <div class="nav-item fs-6 text-gray-900">
                        <div class="d-flex align-items-center justify-content-center">
                            <button type="button" class="align-items-center justify-content-center d-flex btn text-gray-600 bg-transparent text-hover-primary tw-px-[5px] mb-2 tw-w-[30px] custom-object-view-wizard-btn" 
                                hx-vals='{"module": "{{menu_key}}", "drawer-type": "view-settings", "type": "create","object_type":"custom_object"}'
                                hx-get="{% url 'custom_object_drawer' custom_object.id %}"
                                hx-indicator=".loading-drawer-spinner,.view-form"
                                hx-target="#custom-object-view-wizard-drawer"
                                hx-trigger="click"
                                hx-swap="innerHTML"
                                style="height: 26px;"
                                hx-on::before-request="document.querySelector('#custom-object-view-wizard-drawer').innerHTML = '';"
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-plus" viewBox="0 0 16 16">
                                    <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                    
                    {% include "data/projects/partial-dropdown-view-menu.html" %}
                    <div class="{% include "data/utility/view-menu-nav-item.html" %}">
                        <a class="{% include "data/utility/view-menu-default.html" %}" 
                            type="button"
                            href = "{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}">
                            <span class="svg-icon svg-icon-muted svg-icon-2"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M22 12C22 17.5 17.5 22 12 22C6.5 22 2 17.5 2 12C2 6.5 6.5 2 12 2C17.5 2 22 6.5 22 12ZM12 6C8.7 6 6 8.7 6 12C6 15.3 8.7 18 12 18C15.3 18 18 15.3 18 12C18 8.7 15.3 6 12 6Z" fill="currentColor"/>
                                </svg>
                            </span>
                        </a>
                        {% if not view_filter.view.title %}
                        <div class="w-20px nav-item justify-content-center d-flex fs-6 text-gray-900">
                            <button type="button" class="{% include "data/utility/view-plus-link.html" %} custom-object-view-wizard-btn"
                                hx-vals='{"module": "{{menu_key}}", "drawer-type":"view-settings","view_id":"{{view_filter.view.id}}", "type": "update","object_type":"custom_object"}'
                                hx-get="{% url 'custom_object_drawer' custom_object.id %}"
                                hx-indicator=".loading-drawer-spinner,.view-form"
                                hx-target="#custom-object-view-wizard-drawer"
                                hx-trigger="click"
                                hx-swap="innerHTML"
                                hx-on::before-request="document.querySelector('#custom-object-view-wizard-drawer').innerHTML = '';"
                                >
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-three-dots-vertical" viewBox="0 0 16 16">
                                    <path d="M9.5 13a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z"/>
                                </svg>
                            </button>
                        </div>
                        {% endif %}
                    </div>

                    {% include 'data/projects/partial-view-menu.html' %}

                </div>

                {% comment %} Mobile View {% endcomment %}
                <div class="tw-hidden max-md:tw-flex tw-w-1/2">
                    <div class="d-flex align-items-center">
                        <!-- Example split danger button -->
                        <div class="btn-group mb-2">
                            <button type="button" class="tw-max-w-[90px] align-items-center d-flex btn bg-white border btn-md tw-px-[10px] custom-object-view-wizard-btn"
                                style="height: 26px; border-radius: 0.475rem 0 0 0.475rem;"
                                hx-vals='{"module": "{{menu_key}}", "drawer-type":"view-settings","view_id":"{{view_filter.view.id}}", "type": "update","object_type":"custom_object"}'
                                hx-get="{% url 'custom_object_drawer' custom_object.id %}"
                                hx-indicator=".loading-drawer-spinner,.view-form"
                                hx-target="#custom-object-view-wizard-drawer"
                                hx-trigger="click"
                                hx-swap="innerHTML"
                                hx-on::before-request="document.querySelector('#custom-object-view-wizard-drawer').innerHTML = '';"
                            >
                                <span class="fs-7 ps-1 fw-bolder tw-text-ellipsis tw-overflow-hidden tw-text-nowrap">
                                    {% if view_filter.view.title %}
                                        {{ view_filter.view.title }}
                                    {% else %}
                                        {% if LANGUAGE_CODE == 'ja'%}ビュー{% else %}View{% endif %}
                                    {% endif %}
                                </span>
                            </button>
                            <button type="button" 
                                class="tw-w-[30px] align-items-center d-flex btn btn-white border btn-md  dropdown-toggle py-1 tw-pl-2 dropdown-toggle-split" 
                                data-bs-toggle="dropdown" 
                                aria-expanded="false"
                                style="height: 26px; border-radius: 0 0.475rem 0.475rem 0;"
                            >
                                <span class="svg-icon svg-icon-4">
                                    <svg width=24 height=24 xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512" fill="currentColor">
                                        <path d="M137.4 374.6c12.5 12.5 32.8 12.5 45.3 0l128-128c9.2-9.2 11.9-22.9 6.9-34.9s-16.6-19.8-29.6-19.8L32 192c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9l128 128z"/>
                                    </svg>
                                </span>
                            </button>
                            <ul class="dropdown-menu tw-max-w-[180px] tw-max-h-[300px] tw-overflow-y-scroll">
                                <li><a class="dropdown-item tw-text-ellipsis tw-overflow-hidden" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}">{% if LANGUAGE_CODE == 'ja' %}デフォルト{% else %}Default View{% endif %}</a></li>
                                <div class="dropdown-divider"></div>
                                {% for view in views %}
                                    {% if view.title %}
                                        <li><a class="dropdown-item tw-text-ellipsis tw-overflow-hidden" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?view_id={{view.id}}">{{view.title}}</a></li>
                                    {% endif %}
                                {% endfor %}
                            </ul>
                        </div>
                        <div class="fs-6 text-gray-900 mb-2">
                            <button type="button" class="tw-border-0 tw-bg-transparent tw-text-gray-900 custom-object-view-wizard-btn" 
                                hx-vals='{"module": "{{menu_key}}", "drawer-type": "view-settings", "type": "create","object_type":"custom_object"}'
                                hx-get="{% url 'custom_object_drawer' custom_object.id %}"
                                hx-indicator=".loading-drawer-spinner,.view-form"
                                hx-target="#custom-object-view-wizard-drawer"
                                hx-trigger="click"
                                hx-swap="innerHTML"
                                hx-on::before-request="document.querySelector('#custom-object-view-wizard-drawer').innerHTML = '';"
                                >
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-plus" viewBox="0 0 16 16">
                                    <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="d-flex w-50 justify-content-end">

                    {% include "data/common/advance_search/advance-search-component.html" %}

                    <div class="{% include "data/utility/view-menu-search.html" %}">
                        <button 
                            onclick="openSearch()"
                            class="tw-w-[28px] tw-h-[26px] tw-justify-center tw-items-center tw-border-0 tw-bg-transparent tw-mb-2 tw-cursor-pointer hover-tooltip" type="button"
                            >
                            <span class="search-wrapper-tooltip hover-tooltip-text">
                                {% if LANGUAGE_CODE == 'ja' %}検索{% else %}Search{% endif %}
                            </span>
                            <span class="tw-flex svg-icon svg-icon-3">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                    <path d="M21.7 18.9L18.6 15.8C17.9 16.9 16.9 17.9 15.8 18.6L18.9 21.7C19.3 22.1 19.9 22.1 20.3 21.7L21.7 20.3C22.1 19.9 22.1 19.3 21.7 18.9Z" fill="black" />
                                    <path opacity="0.3" d="M11 20C6 20 2 16 2 11C2 6 6 2 11 2C16 2 20 6 20 11C20 16 16 20 11 20ZM11 4C7.1 4 4 7.1 4 11C4 14.9 7.1 18 11 18C14.9 18 18 14.9 18 11C18 7.1 14.9 4 11 4ZM8 11C8 9.3 9.3 8 11 8C11.6 8 12 7.6 12 7C12 6.4 11.6 6 11 6C8.2 6 6 8.2 6 11C6 11.6 6.4 12 7 12C7.6 12 8 11.6 8 11Z" fill="black" />
                                </svg>
                            </span>
                        </button>
                    </div>
                </div>
            </div>
            {% if permission|check_permission:'archive' %}
            <div id="match-response-view" class="mb-1 mt-1"><br></div>
            {% endif %}
            <div id="custom-object-bulk-action-container" class="tw-hidden d-flex w-100">
                <div class="justify-content-between align-items-center flex-row d-flex w-100">
                    <div class="w-100 d-flex">
                        <div class="me-10 d-flex" style="min-width: 30px">
                            <button class="py-1 rounded-1 btn btn-sm btn-light fw-bold mt-2 mb-1 me-2" onclick="selectAllCustomObjects()">
                                <span>
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    すべて選択
                                    {% else %}
                                    Select All
                                    {% endif %}
                                </span>
                            </button>
                            <button class="py-1 rounded-1 btn btn-sm btn-light fw-bold mt-2 mb-1" onclick="deselectAllCustomObjects()">
                                <span>
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    選択を解除
                                    {% else %}
                                    Deselect All
                                    {% endif %}
                                </span>
                            </button>
            
                            <script>
                                function selectAllCustomObjects() {
                                    selectCustomObjectInputs = document.getElementsByClassName('custom-object-selection')
                                    for (var i = 0; i < selectCustomObjectInputs.length; i++) {
                                        selectCustomObjectInputs[i].checked = true;
                                    }
                                }
                                function deselectAllCustomObjects() {
                                    selectCustomObjectInputs = document.getElementsByClassName('custom-object-selection')
                                    for (var i = 0; i < selectCustomObjectInputs.length; i++) {
                                        selectCustomObjectInputs[i].checked = false;
                                    }
                                    document.getElementById('custom-object-bulk-action-container').classList.add('tw-hidden')
                                    document.getElementById('custom-object-view-container').classList.remove('tw-hidden')
                                    document.getElementById('custom-object-view-container-1').classList.remove('tw-hidden')
                                }
                            </script>
                        </div>
                        {% if permission|check_permission:'edit' %}
                        <button class="py-1 rounded-1 btn btn-sm btn-light-warning fw-bold mt-2 mb-1 me-2" name="bulk_duplicate" type="submit" form="custom-object-form">
                            {% if LANGUAGE_CODE == 'ja'%}
                            複製
                            {% else %}
                            Duplicate
                            {% endif %}
                        </button>
                        {% endif %}
                        {% if permission|check_permission:'archive' %}
                        <button class="py-1 rounded-1 btn btn-sm btn-light-success fw-bold mt-2 mb-1 me-2" data-bs-toggle="modal" data-bs-target="#manage_restore_bulk">
                            {% if LANGUAGE_CODE == 'ja'%}
                            有効化
                            {% else %}
                            Activate
                            {% endif %}
                            </button>
                        <button class="btn btn-sm btn-light-danger py-1 rounded-1 fw-bold mt-2 mb-1 me-2" data-bs-toggle="modal" data-bs-target="#manage_delete_bulk">
                            {% if LANGUAGE_CODE == 'ja'%}
                            アーカイブ
                            {% else %}
                            Archive 
                            {% endif %}
                        </button>
                        <button class="btn btn-sm btn-light-primary py-1 rounded-1 fw-bold mt-2 mb-1 me-2"
                            id="match-association-button" 
                            hx-post="{% url 'hubspot_match_custom_object' custom_object.id %}"
                            hx-vals = '{"view_id": "{{view_filter.view.id}}", "custom_object_id": "{{custom_object.id}}"}'
                            hx-indicator=".loading-drawer-spinner"
                            hx-trigger="submitMatchRecord"
                            hx-target="#match-response-view"
                            onclick="handleMatchHubspotObjectClick(this)"
                            hx-on:htmx:afterRequest="handleMatchResponse(event)"
                            >
                            <span id="match-label-btn">
                                {% if LANGUAGE_CODE == 'ja'%}
                                マッチ
                                {% else %}
                                Match
                                {% endif %}
                            </span>
                        </button>
                        
                        
                        {% endif %}
                    </div>
                    <div class="d-flex">
                        {% if permission|check_permission:'edit' %}
                            <div class="{% include "data/utility/table-button.html" %}">
                                <button id='view-sync-items' type="button" class="w-150px align-items-center d-flex justify-content-center svg-icon-gray-600 btn btn-sm py-1 rounded-1 bg-gray-100 custom-object-create-wizard-btn"
                                    style="width: 130px"
                                    hx-get="{% url 'custom_object_drawer' custom_object.id %}"
                                    hx-trigger="click"
                                    onclick="fillCustomObjectExportIds(this)"
                                    hx-target="#custom-object-create-wizard-drawer"
                                    hx-indicator=".loading-drawer-spinner,.view-form"
                                    >
                                    <span class="svg-icon svg-icon-4">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-upload" viewBox="0 0 16 16">
                                            <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5"/>
                                            <path d="M7.646 1.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 2.707V11.5a.5.5 0 0 1-1 0V2.707L5.354 4.854a.5.5 0 1 1-.708-.708z"/>
                                        </svg>
                                    </span>
                                    <span class="">
                                        {% if LANGUAGE_CODE == 'ja'%}
                                        エクスポート
                                        {% else %}
                                        Export
                                        {% endif %}
                                    </span>
                                </button>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% comment %} End of Views {% endcomment %}
    <div class="mb-2 d-none" id="d-select-additional-options">
        <div class="status bg-light-dark text-center pt-4 pb-3 px-5 rounded">
            {% if LANGUAGE_CODE == 'ja'%}
            このページのすべてのレコードが選択されました。 
            {% else %}
            All records on this page are selected. 
            {% endif %}
             <a onclick="toggleText()" 
                class="btn btn-dark" 
                data-bs-toggle="collapse" 
                id="select-additional-options-toggle" 
                role="button" 
                aria-expanded="false" 
                aria-controls="collapseExample">
                {% if LANGUAGE_CODE == 'ja'%}
                全てのレコードを選択する 
                {% else %}
                Select all records. 
                {% endif %}
    
            </a>
        </div>
    </div>


    {% include "data/common/advance_search/advance-search-mobile-component.html" %}

    <form method="POST" id="custom-object-form">
        {% csrf_token %}
        <div class="table-responsive">
            <table class="{% include "data/utility/table.html" %} objects-table">
                <thead class="{% include "data/utility/table-header.html" %}">
                    <tr class="align-middle">
                        {% include "data/custom_object/table_header.html" %}
                    </tr>
                </thead>
                <tbody class="fs-6">
                    {% for obj in custom_object_data %}
                        <tr id="row-{{item.id}}" 
                            hx-get="{% host_url "custom_object_row_details" custom_object.id obj.id host 'app' %}" 
                            hx-trigger="load"
                            hx-vals='js:{ "menu_key": "{{menu_key}}", "view_id": "{{view_filter.view.id}}"}'
                        >
                            <td class="d-flex justify-content-center w-100">
                                <style>
                                    /* Styles for the spinner */
                                    .row_load-{{obj.id}} {
                                        display: none; /* Initially hidden */
                                    }
                                    .htmx-request .row_load-{{obj.id}},
                                    .htmx-request.row_load-{{obj.id}} {
                                        display: inline-block; /* Display during htmx request */
                                    }
                                </style>
                                <!-- Spinner icon -->
                                <span class="spinner-border spinner-border-lg text-secondary row_load-{{obj.id}}" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </span>
                            </td>
                        </tr>
                    {% endfor %}
            </table>

            <input name='flag_all' id='flag_all' class="flag_all" hidden type="checkbox" ></input>
            {% if view_id %}
                <input type="hidden" value="{{view_filter.view.id}}" name="view_id">
            {% endif %}
    
            <div class="modal fade" tabindex="-1" id="manage_delete_bulk">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header pb-0 border-0 justify-content-end">
                            <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                            </div>
                        </div>
                        <div class="modal-body pb-0">
                            <div class="mb-13 text-center">
                                <h3 class="modal-title">
                                    
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    一括アーカイブの確認
                                    {% else %}
                                    Bulk Archive Confirmation
                                    {% endif %}
                                
                                </h3>
                            </div>
                            <div class="border-bottom">
                                <div class="fv-rowd-flex flex-column mb-8">
                                    <label class="{% include 'data/utility/form-label.html' %}">
                                        <span class="">
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            選択されたレコードをアーカイブしてもよろしいですか?
                                            {% else %}
                                            Are you sure to archive selected records?
                                            {% endif %}
                                        </span>
                                    </label>
                            
                                </div>
                            </div>
                        </div>
                        
        
                        <div class="modal-footer border-0">
                            <button name="bulk_delete_objects" type="submit" class="btn btn-danger">
                                
                                {% if LANGUAGE_CODE == 'ja'%}
                                    アーカイブ
                                    {% else %}
                                    Archive
                                    {% endif %}
                                </button>
                            
                            </button>
                            <a data-bs-dismiss="modal" class="btn bg-gray-200 border">
                                {% if LANGUAGE_CODE == 'ja'%}
                                キャンセル
                                {% else %}
                                Cancel
                                {% endif %}
                            </a>
                        </div>
                    </div>
                </div>
            </div> 

            <div class="modal fade" tabindex="-1" id="manage_restore_bulk">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header pb-0 border-0 justify-content-end">
                            <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                            </div>
                        </div>
                        <div class="modal-body pb-0">
                            <div class="mb-13 text-center">
                                <h3 class="modal-title">
                                    
                                    {% if LANGUAGE_CODE == 'ja'%}
                                    一括有効化の確認
                                    {% else %}
                                    Bulk Activation Confirmations
                                    {% endif %}
                                
                                </h3>
                            </div>
                            <div class="border-bottom">
                                <div class="fv-rowd-flex flex-column mb-8">
                                    <label class="{% include 'data/utility/form-label.html' %}">
                                        <span class="">
                                            {% if LANGUAGE_CODE == 'ja'%}
                                            これらの商品を有効化してもよろしいですか?
                                            {% else %}
                                            Are you sure to activate these objects?
                                            {% endif %}
                                        </span>
                                    </label>
                                    
                                </div>
                            </div>
                        </div>
                        
        
                        <div class="modal-footer border-0">
                            <button name="bulk_restore_objects" type="submit" class="btn btn-success">
                                {% if LANGUAGE_CODE == 'ja'%}
                                有効化
                                {% else %}
                                Activate
                                {% endif %}
                            </button>
                            <a data-bs-dismiss="modal" class="btn bg-gray-200 border">
                                {% if LANGUAGE_CODE == 'ja'%}
                                キャンセル
                                {% else %}
                                Cancel
                                {% endif %}
                            </a>
                        </div>
                    </div>
                </div>
            </div> 
        </div>
    </form>

    <div class="{% include "data/utility/pagination.html" %}">
        {% if LANGUAGE_CODE == 'ja'%}
            {{paginator_item_begin}}–{{paginator_item_end}} の {{paginator.count}} 件
        {% else %}
            Viewing {{paginator_item_begin}}–{{paginator_item_end}} of {{paginator.count}} results
        {% endif %}
        
        <div>
            {% if page_content.has_previous %}     
                <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300  w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page=1&{% query_transform %}">&laquo; {% translate_lang "First" LANGUAGE_CODE %}</a>
                <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300  w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page={{ page_content.previous_page_number }}&{% query_transform %}">&lsaquo; {% translate_lang "Previous" LANGUAGE_CODE %}</a>
            {% endif %}
                    
            {% if page_content.has_next %}
                <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300  w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page={{ page_content.next_page_number }}&{% query_transform %}">{% translate_lang "Next" LANGUAGE_CODE %} &rsaquo;</a>
                <a class="btn tw-bg-gray-200 hover:tw-bg-gray-300  w-100px" href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?page={{ page_content.paginator.num_pages }}&{% query_transform %}">{% translate_lang "Last" LANGUAGE_CODE %} &raquo;</a>
            {% endif %}
        </div>
    </div>
</div>
{% endif %}

<div class="d-flex align-items-center">
    <button class="align-items-center d-flex btn btn-dark-outline btn-md custom-object-manage-wizard-btn py-1 text-dark text-hover-primary fw-bolder px-1 custom_object_row_{{row.id}}" type="button"
        hx-get="{% url 'custom_object_drawer' custom_object.id %}"
        hx-vals = '{"drawer-type":"manage", "row_id":"{{row.id}}", "view_id":"{{view_id}}", "page": "{{page}}", "module": "{{menu_key}}" }'
        hx-target="#custom-object-manage-wizard-drawer"
        hx-indicator=".loading-drawer-spinner,#custom-object-manage-wizard-drawer"
        >
        {{ row.row_id|stringformat:"04d" }}
    </button>
</div> 

{% if isopen and isopen != '' %}
    <a
        hx-get="{% url 'custom_object_drawer' custom_object.id %}"
        hx-target="#custom-object-manage-wizard-drawer"
        hx-vals = '{"drawer-type":"manage", "row_id":"{{isopen}}", "view_id":"{{view_id}}", "page": "{{page}}", "module": "{{menu_key}}" }'
        hx-on="htmx:beforeSend: 
        document.getElementById('custom-object-manage-wizard-drawer').innerHTML = '';
            "
        hx-trigger="click"
        hx-indicator=".loading-drawer-spinner,#custom-object-manage-wizard-drawer"
        class="text-center mb-0 text-dark text-hover-primary fw-bolder custom-object-manage-wizard-btn cursor-pointer custom_object_open">
    </a>
{% endif %}

<script>
    {% if isopen %}
    var button = document.querySelector('.custom_object_open');
    
    function clickButton() {
        if (button) {
            button.click(); // Simulate a button click
        }
    }

    
    // Add an event listener to run the clickButton function after the page has loaded
    window.onload = function() {
        clickButton();
    };
    {% endif %}

    // Function to load script dynamically
    function loadScript(url, callback) {
        var script = document.createElement('script');
        script.type = 'text/javascript';
        script.src = url;
        script.onload = callback;
        script.onerror = function() {
            console.error('Failed to load script:', url);
        };
        document.head.appendChild(script);
    }

    // Check if jQuery is loaded, if not, load it
    function ensureJQuery(callback) {
        if (typeof window.jQuery === 'undefined') {
            console.log('jQuery not loaded, loading now...');
            loadScript('https://cdn.jsdelivr.net/jquery/latest/jquery.min.js', function() {
                console.log('jQuery loaded successfully');
                if (callback) callback();
            });
        } else {
            console.log('jQuery already loaded');
            if (callback) callback();
        }
    }

    // Check if DataTable is loaded, if not, load it
    function ensureDataTableScripts(callback) {
        if (typeof $.fn.DataTable === 'undefined') {
            console.log('DataTable not loaded, loading scripts...');
            loadScript('https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js', function() {
                console.log('DataTable core loaded');
                loadScript('https://cdn.datatables.net/fixedcolumns/4.3.0/js/dataTables.fixedColumns.min.js', function() {
                    console.log('DataTable FixedColumns loaded');
                    if (callback) callback();
                });
            });
        } else {
            console.log('DataTable already loaded');
            if (callback) callback();
        }
    }
    
    var table = null;
    var isDataTableInitialized = false;
    var requestNum = {% if custom_object_data|length == 0 %}1{% else %}{{custom_object_data|length}}{% endif %}
    document.addEventListener('htmx:afterRequest', function(evt) {
        if (requestNum <= 0) return;

        if (evt.target.tagName.toLowerCase() === 'tr') {
            requestNum--;

            // Only initialize DataTable once, after all rows are loaded
            if (requestNum === 0 && !isDataTableInitialized) {
                ensureDataTableScripts(function() {
                    // Check if DataTable is already initialized
                    if ($.fn.DataTable.isDataTable('.objects-table')) {
                        console.log('DataTable already initialized, skipping...');
                        return;
                    }

                    table = $(".objects-table").DataTable({
                        responsive: true,
                        scrollX:        true,
                        scrollCollapse: true,
                        fixedColumns:   {
                            left: 3
                        },
                        ordering: false,
                        searching: false,  // Hide the search bar
                        paging: false,      // Hide pagination
                        info: false,        // Hide the information text
                        language: {
                            emptyTable: "{% translate_lang 'No data available in table' LANGUAGE_CODE %}"
                        }
                    });

                    isDataTableInitialized = true;
                    console.log('DataTable initialized successfully for custom objects');

                    {% include "data/common/open-drawer.js" %}
                })
            }
        }

    });

    function refresh_table() {
        if (table != null) {
            table.columns.adjust()
        }
    }

    function getHTMLFromFragment(fragment) {
        const tempDiv = document.createElement('div');
        tempDiv.appendChild(fragment.cloneNode(true)); // Clone to avoid removing nodes
        return tempDiv.innerHTML;
    }

    function getAllNextSiblings(element) {
        const siblings = [];
        let nextSibling = element.nextElementSibling;
    
        while (nextSibling) {
            siblings.push(nextSibling);
            nextSibling = nextSibling.nextElementSibling;
        }
    
        return siblings;
    }

    var index_1 = '';
    var index_2 = '';
    const checking_checkbox = (elem,event) => {
        
        if (event.shiftKey) {
            check_inputs = document.getElementsByClassName('custom-object-selection')
            
            index_2 = elem;

            //Do Check
            var pos_1 = '';
            var pos_2 = '';
            for (var i = 0; i < check_inputs.length; i++) {
                if (index_1 == check_inputs[i]){
                    pos_1 = i;
                }
                else if (index_2 == check_inputs[i]){
                    pos_2 = i;
                }
            }
            
            if (pos_1 > pos_2){
                for (var i = pos_2; i < pos_1; i++) {
                    check_inputs[i].checked = true;
                }
            }
            else {
                for (var i = pos_1; i < pos_2; i++) {
                    console.log(i);
                    check_inputs[i].checked = true;
                    
                }
            }
        }
        else{
            if (elem.checked) {
                index_1 = elem;
                document.getElementById('custom-object-bulk-action-container').classList.remove('tw-hidden')
                document.getElementById('custom-object-view-container').classList.add('tw-hidden')
                document.getElementById('custom-object-view-container-1').classList.add('tw-hidden')
            } else {
                CustomObjectSelections = document.getElementsByClassName('custom-object-selection')
                for (let i = 0; i < CustomObjectSelections.length; i++) {
                    const element = CustomObjectSelections[i];
                    if (element.checked) {
                        return
                    }
                }
                document.getElementById('custom-object-bulk-action-container').classList.add('tw-hidden')
                document.getElementById('custom-object-view-container').classList.remove('tw-hidden')
                document.getElementById('custom-object-view-container-1').classList.remove('tw-hidden')
            }
        }
    }

    function fillCustomObjectExportIds(elm) {
        // Call your JavaScript function to generate the account IDs dynamically
        var checkboxes = document.querySelectorAll('input[type="checkbox"]');
        var checkedIds = [];
        checkboxes.forEach(function(checkbox) {
            if (checkbox.checked) {
                checkedIds.push(checkbox.value);
            }
        });
        var customObjectIds = checkedIds;
        customObjectIds = customObjectIds.filter(id => id !== 'on');
        // Now set the hx-vals attribute with the updated account IDs
        elm.setAttribute('hx-vals', '{"drawer-type":"custom-object-view-export-import","page": "{{custom_object.slug}}","import_export_type":"export", "view_id":"{{view_filter.view.id}}", "module": "{{menu_key}}", "custom_object_ids":"' + customObjectIds + '"}');

    }


    function getCookieHubspot(name) {
        const cookieValue = document.cookie
            .split('; ')
            .find(row => row.startsWith(name + '='));
        
        if (cookieValue) {
            return cookieValue.split('=')[1];
        }
        return null;
    }
    function handleMatchHubspotObjectClick(element){

        var currentHxVals = element.getAttribute('hx-vals');
        var currentHxValsObject = JSON.parse(currentHxVals);
        
        const allCookies = document.cookie;
        let from_hubspot = '' 
        const cookiePairs = allCookies.split('; ');
        cookiePairs.forEach(pair => {
            const [name, value] = pair.split('=');
            currentHxValsObject[name] = value;
            if (name === 'from_hubspot') 
                from_hubspot = value
        });

        var checked_record = []
        check_inputs = document.getElementsByClassName('custom-object-selection')
        for (var i = 0; i < check_inputs.length; i++) {
            if (check_inputs[i].checked === true){
                checked_record.push(check_inputs[i].value)
            } 
        }

        if (checked_record.length > 0 && (from_hubspot !== null || from_hubspot !== '')) {
            currentHxValsObject.custom_object_record_ids = checked_record
            element.setAttribute('hx-vals', JSON.stringify(currentHxValsObject));

            htmx.trigger('#match-association-button', 'submitMatchRecord');

            const matchLabel = document.getElementById('match-label-btn');
                    
            // Hide the button text and show loading spinner
            matchLabel.classList.add('d-none');
            element.insertAdjacentHTML('beforeend', `
                <div id="loading-icon" class="mb-2">
                    <div id="loading-spinner" class="text-center">
                        <div class="spinner-border spinner-border-sm text-black" role="status"></div>
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            `);
        } 
    }

    function handleMatchResponse(event) {
        const btnElement = event.target;
        const matchLabel = document.getElementById('match-label-btn');
        const responseView = document.getElementById('match-response-view');        

        // Remove loading spinner
        const loadingIcon = document.getElementById('loading-icon');
        loadingIcon.remove();
                    
        matchLabel.classList.remove('d-none');

        // Show response view and update with response
        responseView.classList.remove('d-none');
        const response = event.detail.xhr.responseText;
        console.log("Received Response:", response);
        responseView.innerHTML = response;
        
        // Add appropriate status class based on HTTP response
        const isSuccess = event.detail.xhr.status >= 200 && event.detail.xhr.status < 300;
        const statusClass = isSuccess ? 'text-success' : 'text-danger';
        responseView.classList.add(statusClass);
        
        // Reset everything after timeout
        setTimeout(() => {
            // Reset response view
            responseView.classList.remove(statusClass);
            responseView.innerHTML = '<br>';
        }, 3000);
    }

    document.addEventListener('DOMContentLoaded', function() {
        const button = document.getElementById('match-association-button');
        
        button.addEventListener('htmx:afterRequest', function(event) {
            handleMatchResponse(event);
        });

        const allCookies = document.cookie;
        let from_hubspot = '' 
        const cookiePairs = allCookies.split('; ');
        cookiePairs.forEach(pair => {
            const [name, value] = pair.split('=');
            if (name === 'from_hubspot') 
                from_hubspot = value
        });
        if (from_hubspot) {
            button.classList.remove('d-none');
        } else {
            button.classList.add('d-none');
        }
    });

    
    function toggleText() {
        var x = document.getElementById("select-additional-options-toggle");
        var toggle_data = x.getAttribute('toggle-data')
        if (toggle_data !== "true") {
            {% if LANGUAGE_CODE == 'ja'%}
            x.innerHTML = "選択を解除";
            {% else %}
            x.innerHTML = "Clear All";
            {% endif %}

          $(".flag_all").each(function(index, element) {
                element.value = true
            });
        
            x.setAttribute('toggle-data',"true")
    

        } else {

            x.setAttribute('toggle-data',"false")

   
            $('input[type=checkbox]').prop('checked', false);

            //Hide
            var element_select_options = document.getElementById("d-select-additional-options");
            element_select_options.classList.add("d-none")

            x.innerHTML = "{% if LANGUAGE_CODE == 'ja'%}すべて選択 ({{paginator.count}}) このセクションの連絡先{%else%}Select All ({{paginator.count}}) contacts in this sections{%endif%}";
            
            $(".flag_all").each(function(index, element) {
                element.value = false
            });

        }
      }
</script>

{% endblock%}