{% load static %}
{% load humanize %}
{% load i18n %}
{% load hosts %}
{% load custom_tags %}


<style>
    table {
        width: auto;
    }
    
    td, th {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    
</style>

<style>
    .search-wrapper {
        display: flex;
        align-items: center;
        position: relative;
        width: 24px;
        transition: width 0.4s ease-in-out, margin-right 0.4s ease-in-out, opacity 0.4s ease-in-out;
    }

    .search-wrapper.expanded {
        width: 200px; /* New width when expanded */
        margin-right: -0.5rem !important;
    }

    .search-wrapper input {
        display: none;
        width: 0;
        padding: 0;
        opacity: 0;
        transition: width 0.4s ease-in-out, margin-right 0.4s ease-in-out, opacity 0.4s ease-in-out;
    }

    .search-wrapper.expanded input {
        display: block;
        width: 100%;
        opacity: 1;
    }
    

    .search-icon-view {
        position: absolute;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
    }

    /* Tooltip container */
    .hover-tooltip {
        position: relative;
        display: inline-block;
    }

    /* Tooltip text */
    .hover-tooltip .hover-tooltip-text {
        visibility: hidden;
        width: 80px;
        background-color: #555;
        color: #fff;
        text-align: center;
        padding: 3px 0;
        border-radius: 8px;

        /* Position the hover-tooltip text */
        position: absolute;
        z-index: 1;
        top: 50%;
        right: 105%;
        transform: translateY(-50%);

        /* Fade in hover-tooltip */
        opacity: 0;
        transition: opacity 0.5s;
    }

    /* Show the hover-tooltip text when you mouse over the hover-tooltip container */
    .hover-tooltip:hover .hover-tooltip-text {
        visibility: visible;
        opacity: 0.9;
    }
</style>

<div class="d-flex justify-content-between align-items-end border-bottom border-bottom-1 {% include "data/utility/tab-pane.html" %}" role="tabpanel" style="z-index:4 !important; top: 0px !important">
    <div class="nav nav-tabs nav-line-tabs fs-6 justify-content-between">
        <div class="{% include "data/utility/table-content.html" %}">
            {% for view in views %}
                {% if view.title == "main" or not view.title %}
                    <div class="{% include "data/utility/view-menu-default-common.html" %} me-5 mh-100px scroll-y">
                        <a  href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?view_id={{view.id}}"
                            class="{% include "data/utility/view-menu-default-2.html" %}" >
                            {% comment %} <span class="svg-icon svg-icon-muted svg-icon-2"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M22 12C22 17.5 17.5 22 12 22C6.5 22 2 17.5 2 12C2 6.5 6.5 2 12 2C17.5 2 22 6.5 22 12ZM12 6C8.7 6 6 8.7 6 12C6 15.3 8.7 18 12 18C15.3 18 18 15.3 18 12C18 8.7 15.3 6 12 6Z" fill="currentColor"/>
                                </svg>
                            </span> {% endcomment %}
                            {% if LANGUAGE_CODE == 'ja'%}ビュー{% else %}View{% endif %}
                        </a>

                        {% comment %} edit {% endcomment %}
                        {% if current_view.id == view.id %}
                        <div class="w-20px nav-item justify-content-center d-flex fs-6 text-gray-900">
                            <button  type="button" class="{% include "data/utility/view-plus-link.html" %} create-content-wizard-button"
                                hx-vals='{"object_type": "{{object_type}}", "view_id":"{{view.id}}"}'
                                hx-get="{% url 'commerce_view_setting' %}" 
                                hx-target="#manage-drawer-campaigns"
                                
                                >
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-three-dots-vertical" viewBox="0 0 16 16">
                                    <path d="M9.5 13a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z"/>
                                </svg>

                            </button>
                        </div>
                        {% endif %}
                    </div>
                {% else %}
                    <div class="{% include "data/utility/view-menu-default-common.html" %} me-5 mh-100px scroll-y">
                        <a  href="{% host_url 'load_object_page' menu_key object_type|object_type_slug:request host 'app' %}?view_id={{view.id}}"
                            class="{% include "data/utility/view-menu-default-2.html" %}" >
                            <div class="fw-bolder">
                                {{view.title}}
                            </div>
                        </a>

                        {% comment %} edit {% endcomment %}
                        {% if current_view.id == view.id %}
                        <div class="w-20px nav-item justify-content-center d-flex fs-6 text-gray-900">
                            <button  type="button" class="{% include "data/utility/view-plus-link.html" %} create-content-wizard-button"
                                
                                hx-vals='{"object_type": "{{object_type}}", "view_id":"{{view.id}}"}'
                                hx-get="{% url 'commerce_view_setting' %}" 
                                hx-target="#manage-drawer-campaigns"
                                
                                >
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-three-dots-vertical" viewBox="0 0 16 16">
                                    <path d="M9.5 13a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z"/>
                                </svg>

                            </button>
                        </div>
                        {% endif %}
                    </div>
                {% endif %}
            {% endfor %}

            <div class="nav-item  fs-6 text-gray-900">
                <button  type="button" class="nav-link mx-1 create-content-wizard-button" 
                    hx-vals='{"object_type": "{{object_type}}", "view_button":"create"}'
                    hx-get="{% url 'commerce_view_setting' %}" 
                    hx-target="#manage-drawer-campaigns"
                >
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-plus" viewBox="0 0 16 16">
                        <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                    </svg>
                </button>
            </div>
        </div>
    </div>
    

    <div class="d-flex align-items-center mb-3">
        <div class="me-2  {% if view_filter.view_type == 'calendar' %}d-none{% endif %}">
            <div class="me-2 search-wrapper expanded  hover-tooltip">
                <span class="search-wrapper-tooltip hover-tooltip-text">
                    {% if LANGUAGE_CODE == 'ja' %}検索{% else %}Search{% endif %}
                </span>
                <div class="d-flex align-items-center">
                    <form id="filter-form-search" method="get" class="w-100">
                        <div class="d-flex mb-0 position-relative align-items-center" style="height: 22px;">
                            <span class="svg-icon svg-icon-3 search-icon-view" onclick="toggleSearch()">
                                <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M13 13L10.1 10.1M11.6667 6.33333C11.6667 9.27885 9.27885 11.6667 6.33333 11.6667C3.38781 11.6667 1 9.27885 1 6.33333C1 3.38781 3.38781 1 6.33333 1C9.27885 1 11.6667 3.38781 11.6667 6.33333Z" stroke="#8E8C95" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </span>
                            <input
                            id="base-search-input" type="text" name="q" class="form-control bg-white ps-12 h-25px me-2 tw-rounded-lg"
                            value={% if search_q %} "{{ search_q }}" {% else %}""{% endif %}
                            placeholder={% if LANGUAGE_CODE == 'ja' %} "検索" {% else %} "Search" {% endif %}
                            onkeypress="if (event.keyCode == 13)document.forms['filter-form-search'].submit();"
                            >
                            <input type="hidden" value="{{view_id}}" name="view_id">
                        </div>
                    </form>
                </div>
            </div>
        </div>

        {% if enable_sync %}
        {% endif%}

        {% if enable_create %}
        {% endif %}
    </div>
</div>


<div id="contact-bulk-action-container" class="d-none justify-content-between align-items-end flex-row border-bottom-1 border-bottom my-5 {% include "data/utility/tab-pane.html" %}">
    <span class="me-10">
        <button class="py-1 rounded-1 btn btn-sm btn-light fw-bold mt-2 mb-1" onclick="selectAllContacts()">
            <span>
                {% if LANGUAGE_CODE == 'ja'%}
                すべて選択
                {% else %}
                Select All
                {% endif %}
            </span>
        </button>
        <button class="py-1 rounded-1 btn btn-sm btn-light fw-bold mt-2 mb-1" onclick="deselectAllContacts()">
            <span>
                {% if LANGUAGE_CODE == 'ja'%}
                選択を解除
                {% else %}
                Deselect All
                {% endif %}
            </span>
        </button>

        <script>
            function selectAllContacts() {            
                $('input[type=checkbox]').prop('checked', true);
            }
            function deselectAllContacts() {        
                $('input[type=checkbox]').prop('checked', false);
                document.getElementById('contact-bulk-action-container').classList.add('d-none')
                document.getElementById('contact-view-container').classList.remove('d-none')
            }
        </script>
    </span>

    <button class="btn btn-sm btn-light-danger py-1 rounded-1 fw-bold mt-2 mb-1" name="bulk_archive" type="submit" form="bulk-delete">
        {% if LANGUAGE_CODE == 'ja'%}
        削除
        {% else %}
        Delete 
        {% endif %}
    </button>

    <script>
        document.body.addEventListener("hideTaskBulkActionBtn", function(evt){
            document.getElementById('task-bulk-action-container').classList.add('d-none')
            document.getElementById('task-view-contianer').classList.remove('d-none')
        })
    </script>
</div>
 

<script>
    const checking_checkbox = (elem) => {
        if (elem.checked) {
            document.getElementById('contact-bulk-action-container').classList.remove('d-none')
            document.getElementById('contact-view-container').classList.add('d-none')
        } else {
            taskSelections = document.querySelectorAll('input[type=checkbox]')
            for (let i = 0; i < taskSelections.length; i++) {
                const element = taskSelections[i];
                console.log(element.checked)
                if (element.checked) {
                    return
                }
            }
            document.getElementById('contact-bulk-action-container').classList.add('d-none')
            document.getElementById('contact-view-container').classList.remove('d-none')
        }
    }
</script>

{% include 'data/javascript/toggleSearch.html' %}